#!/usr/bin/env python3
"""
Test to compare API vs Local file processing
"""

import os
import sys
import subprocess

def test_local_file_detection():
    """Test local file format detection"""
    print("🔧 TESTING LOCAL FILE FORMAT DETECTION")
    print("=" * 60)
    
    input_file = "USA AI Datacenter Demand (1).xlsx"
    
    if not os.path.exists(input_file):
        print(f"❌ Local file not found: {input_file}")
        return None
    
    # Test local processing with detailed logs
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--output_file", "test_local_detection.csv",
        "--overwrite"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        print(f"Return code: {result.returncode}")
        
        # Extract key information from logs
        logs = result.stdout.split('\n')
        
        format_info = {}
        for line in logs:
            if "Identified format:" in line:
                format_info['format'] = line.split("Identified format: ")[1].strip()
            elif "Parser returned" in line and "data points" in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "returned" and i+1 < len(parts):
                        format_info['data_points'] = parts[i+1]
                        break
            elif "Found" in line and "entries for" in line and "unique dates" in line:
                format_info['unique_dates_info'] = line.strip()
            elif "Found" in line and "valid entries" in line:
                format_info['valid_entries_info'] = line.strip()
            elif "Analyzing DataFrame with columns:" in line:
                format_info['columns'] = line.split("columns: ")[1].strip()
        
        print(f"📊 LOCAL DETECTION RESULTS:")
        for key, value in format_info.items():
            print(f"   {key}: {value}")
        
        return format_info
        
    except Exception as e:
        print(f"❌ Local test failed: {e}")
        return None

def analyze_file_directly():
    """Analyze the file directly to see its structure"""
    print(f"\n🔍 DIRECT FILE ANALYSIS")
    print("=" * 60)
    
    try:
        # Import the file processor to analyze the file directly
        from file_processor import FileProcessor
        
        file_processor = FileProcessor()
        
        # Process the file to see what it contains
        data, file_format = file_processor.process_file("USA AI Datacenter Demand (1).xlsx")
        
        print(f"📁 FILE STRUCTURE:")
        print(f"   Format detected: {file_format}")
        print(f"   Data shape: {data.shape if hasattr(data, 'shape') else 'Unknown'}")
        print(f"   Columns: {list(data.columns) if hasattr(data, 'columns') else 'Unknown'}")
        
        if hasattr(data, 'head'):
            print(f"   First 5 rows:")
            print(data.head().to_string(index=False))
        
        # Check for timestamp information
        if hasattr(data, 'columns'):
            timestamp_cols = [col for col in data.columns if any(word in col.lower() for word in ['date', 'time', 'timestamp'])]
            print(f"   Timestamp columns: {timestamp_cols}")
            
            if timestamp_cols:
                first_col = timestamp_cols[0]
                print(f"   Sample {first_col} values:")
                for i in range(min(5, len(data))):
                    print(f"      {data.iloc[i][first_col]}")
        
        return {
            'format': file_format,
            'shape': data.shape if hasattr(data, 'shape') else None,
            'columns': list(data.columns) if hasattr(data, 'columns') else None
        }
        
    except Exception as e:
        print(f"❌ Direct analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_api_expectations():
    """Compare what API vs Local should detect"""
    print(f"\n📊 API vs LOCAL COMPARISON")
    print("=" * 60)
    
    print(f"🔍 EXPECTED BEHAVIOR:")
    print(f"   • Same file should produce same format detection")
    print(f"   • Same format should produce same data point count")
    print(f"   • Same processing should produce same results")
    
    print(f"\n🚨 OBSERVED DIFFERENCES:")
    print(f"   API:")
    print(f"     - Format: daily")
    print(f"     - Data points: 210,240")
    print(f"     - Detection: '8759 unique dates (1.0 entries per date)'")
    
    print(f"   Local:")
    print(f"     - Format: timestamp")
    print(f"     - Data points: 8,760")
    print(f"     - Detection: '8760 valid entries'")
    
    print(f"\n🎯 POSSIBLE CAUSES:")
    print(f"   1. Different file content (S3 vs local)")
    print(f"   2. Different file reading logic (S3 vs local)")
    print(f"   3. Different format detection parameters")
    print(f"   4. Different parser selection logic")

def main():
    """Run API vs Local comparison"""
    print("🔍 API vs LOCAL FILE PROCESSING COMPARISON")
    print("=" * 70)
    
    # Test 1: Local detection
    local_info = test_local_file_detection()
    
    # Test 2: Direct analysis
    direct_info = analyze_file_directly()
    
    # Test 3: Compare expectations
    compare_api_expectations()
    
    print(f"\n" + "=" * 70)
    print("🎯 DIAGNOSIS:")
    
    if local_info and direct_info:
        if local_info.get('format') == 'timestamp' and direct_info.get('format') == 'timestamp':
            print("✅ Local processing is consistent")
            print("❌ API is detecting different format for same file")
            print("\n🔧 SOLUTION NEEDED:")
            print("  • Check if API is reading the same file")
            print("  • Verify S3 file content matches local file")
            print("  • Ensure API uses same format detection logic")
        else:
            print("❌ Inconsistent format detection even locally")
            print("🔧 Need to debug format detection logic")
    else:
        print("❌ Could not complete analysis")
        print("🔧 Check file access and processing logic")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
