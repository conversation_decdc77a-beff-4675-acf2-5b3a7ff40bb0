import os
import pandas as pd
import tempfile
import subprocess
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_excel_file(excel_file_path, output_file='output.json', use_s3=False, bucket_name=None, overwrite=True):
    """
    Process an Excel file by converting it to CSV and then using the existing system

    Args:
        excel_file_path: Path to the Excel file
        output_file: Name of the output JSON file
        use_s3: Whether to use S3 for output
        bucket_name: Optional S3 bucket name
        overwrite: Whether to overwrite existing output

    Returns:
        Path to the output JSON file
    """
    # Create a temporary directory to store the CSV file
    with tempfile.TemporaryDirectory() as temp_dir:
        # Get the filename without extension
        file_name = Path(excel_file_path).stem

        # Create a path for the CSV file
        csv_file_path = os.path.join(temp_dir, f"{file_name}.csv")

        # Read the Excel file (first sheet) and convert to CSV
        try:
            # Read only the first sheet as specified
            df = pd.read_excel(excel_file_path, sheet_name=0)
            df.to_csv(csv_file_path, index=False)
            logger.info(f"Converted Excel file to CSV: {csv_file_path}")
        except Exception as e:
            logger.error(f"Error converting Excel file: {str(e)}")
            return None

        # Build the command to process the CSV file
        cmd = ["python", "main_s3.py", "--file", csv_file_path, "--output_file", output_file]

        if use_s3:
            cmd.append("--s3")
            if bucket_name:
                cmd.extend(["--bucket", bucket_name])

        if overwrite:
            cmd.append("--overwrite")

        # Run the command
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"Error processing file: {result.stderr}")
                return None

            logger.info("File processed successfully!")
            return output_file
        except Exception as e:
            logger.error(f"Error running processor: {str(e)}")
            return None

# Example usage
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Process Excel files for demand data')
    parser.add_argument('--file', type=str, required=True, help='Path to Excel file')
    parser.add_argument('--output', type=str, default='output.json', help='Output JSON file')
    parser.add_argument('--s3', action='store_true', help='Use S3 for output')
    parser.add_argument('--bucket', type=str, help='S3 bucket name')
    parser.add_argument('--overwrite', action='store_true', default=True, help='Overwrite existing output')

    args = parser.parse_args()

    process_excel_file(args.file, args.output, args.s3, args.bucket, args.overwrite)
