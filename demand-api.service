[Unit]
Description=Demand Data Pipeline API
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/SE-CLEMAI-DemandAPI
ExecStart=/home/<USER>/SE-CLEMAI-DemandAPI/venv/bin/gunicorn -c gunicorn_conf.py app.main:app
#WorkingDirectory=/home/<USER>/demand-api
#ExecStart=/home/<USER>/demand-api/venv/bin/gunicorn -c gunicorn_conf.py app.main:app
Restart=always
RestartSec=5
Environment="PATH=/home/<USER>/SE-CLEMAI-DemandAPI/venv/bin"
#Environment="PATH=/home/<USER>/demand-api/venv/bin"

[Install]
WantedBy=multi-user.target
