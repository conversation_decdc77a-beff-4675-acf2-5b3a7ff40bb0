import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Tuple, Optional, Union
from datetime import datetime
import logging
from timestamp_handler import TimestampHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaseParser:
    """
    Base class for all parsers
    """

    def __init__(self, detected_format: str = None):
        """
        Initialize the BaseParser

        Args:
            detected_format: Detected date format ('DMY', 'MDY', 'YMD', or None)
        """
        self.timestamp_handler = TimestampHandler(detected_format=detected_format)

    def set_timestamp_handler(self, timestamp_handler: TimestampHandler):
        """
        Set the timestamp handler

        Args:
            timestamp_handler: TimestampHandler instance
        """
        self.timestamp_handler = timestamp_handler

    def parse(self, data: Any) -> List[Tuple[datetime, float]]:
        """
        Parse the data and return a list of (timestamp, demand) tuples

        Args:
            data: The data to parse

        Returns:
            List of (timestamp, demand) tuples
        """
        raise NotImplementedError("Subclasses must implement parse method")

    def _extract_demand_unit(self, column_name: str) -> str:
        """
        Extract the demand unit from a column name

        Args:
            column_name: The column name

        Returns:
            Unit string ('MW', 'kW', etc.)
        """
        column_name = column_name.upper()  # Convert to uppercase for case-insensitive matching

        # Check for megawatt variations
        if 'MW' in column_name or 'MEGAWATT' in column_name:
            return 'MW'
        # Check for kilowatt variations
        elif any(unit in column_name for unit in ['KW', 'KWH', 'KILOWATT']):
            return 'kW'
        # Check for watt variations
        elif any(unit in column_name for unit in ['W', 'WH', 'WATT']):
            return 'W'
        # Check for gigawatt variations
        elif any(unit in column_name for unit in ['GW', 'GWH', 'GIGAWATT']):
            return 'GW'
        else:
            return 'unknown'

    def _find_demand_column(self, columns: List[str]) -> str:
        """
        Find the demand column in a list of columns

        Args:
            columns: List of column names

        Returns:
            Demand column name
        """
        demand_keywords = ['demand', 'energy', 'consumption', 'power']

        for col in columns:
            if any(keyword in col.lower() for keyword in demand_keywords):
                return col

        # If no demand column found, use the last column as a fallback
        if len(columns) > 1:
            return columns[-1]

        return None


class DayHourParser(BaseParser):
    """
    Parser for day-hour format
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse day-hour format data

        Args:
            data: DataFrame with Day and Hour columns

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in day-hour format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        # Default values for year and month (only used if we can't extract from Date)
        default_year = datetime.now().year
        default_month = 1  # Default to January

        for _, row in data.iterrows():
            # Initialize with default values
            year = default_year
            month = default_month
            day = 1

            # Check if we have 'Day' or 'Date' column
            if 'Day' in data.columns:
                day = int(row['Day'])
            else:  # Use 'Date' column
                date_str = str(row['Date'])
                # Extract date components from date string (assuming format like '2023-01-01 00:00:00')
                if '-' in date_str:
                    parts = date_str.split('-')
                    if len(parts) >= 3:
                        try:
                            year = int(parts[0])
                            month = int(parts[1])
                            day = int(parts[2].split(' ')[0])
                        except:
                            logger.warning(f"Could not parse date components from: {date_str}")
                else:
                    # Try other formats
                    try:
                        timestamp = self.timestamp_handler.parse_timestamp(date_str)
                        if timestamp:
                            year = timestamp.year
                            month = timestamp.month
                            day = timestamp.day
                    except:
                        logger.warning(f"Could not parse date from: {date_str}")
            # Find the Hour column (might have trailing spaces)
            hour_col = next((col for col in data.columns if col.strip() == 'Hour'), None)
            if hour_col:
                hour = int(row[hour_col])
            else:
                hour = 0  # Default to hour 0 if no Hour column found

            # Construct timestamp
            timestamp = self.timestamp_handler.construct_timestamp_from_components(
                year=year, month=month, day=day, hour=hour
            )

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))

        return result, unit


class TimestampParser(BaseParser):
    """
    Parser for timestamp format
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse timestamp format data

        Args:
            data: DataFrame with a timestamp column

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find timestamp column
        timestamp_cols = [col for col in data.columns if 'time' in col.lower() or 'date' in col.lower()]
        if not timestamp_cols:
            logger.error("No timestamp column found")
            return result

        timestamp_col = timestamp_cols[0]

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in timestamp format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            timestamp_str = str(row[timestamp_col])
            timestamp = self.timestamp_handler.parse_timestamp(timestamp_str)

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))

        return result, unit


class YearMonthDayHourParser(BaseParser):
    """
    Parser for year-month-day-hour format
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse year-month-day-hour format data

        Args:
            data: DataFrame with Year, Month, Day, and Hour columns

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in year-month-day-hour format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            year = int(row['Year'])
            month = int(row['Month'])
            day = int(row['Day'])
            hour = int(row['Hour'])

            # Construct timestamp
            timestamp = self.timestamp_handler.construct_timestamp_from_components(
                year=year, month=month, day=day, hour=hour
            )

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))

        return result, unit


class JsonTimestampParser(BaseParser):
    """
    Parser for JSON timestamp format
    """

    def parse(self, data: List[Dict[str, Any]]) -> List[Tuple[datetime, float]]:
        """
        Parse JSON timestamp format data

        Args:
            data: List of dictionaries with timestamp and demand

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Determine demand key
        demand_keys = ['demand_kW', 'demand', 'power', 'energy', 'consumption']
        demand_key = None
        for key in demand_keys:
            if key in data[0]:
                demand_key = key
                break

        if not demand_key:
            logger.error("No demand key found in JSON format")
            return result

        # Extract unit
        unit = 'kW' if 'kW' in demand_key else 'unknown'

        for item in data:
            timestamp_str = item['timestamp']
            timestamp = self.timestamp_handler.parse_timestamp(timestamp_str)

            if timestamp:
                demand = float(item[demand_key])
                result.append((timestamp, demand))

        return result, unit


class UnixTimestampParser(BaseParser):
    """
    Parser for Unix timestamp format
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse Unix timestamp format data

        Args:
            data: DataFrame with a Unix timestamp column

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find timestamp column
        timestamp_cols = [col for col in data.columns if 'time' in col.lower() or 'date' in col.lower()]
        if not timestamp_cols:
            logger.error("No timestamp column found in Unix timestamp format")
            return result, 'unknown'

        timestamp_col = timestamp_cols[0]

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in Unix timestamp format")
            return result, 'unknown'

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            try:
                # Convert string to integer for Unix timestamp
                unix_timestamp = int(row[timestamp_col])
                timestamp = self.timestamp_handler.parse_timestamp(unix_timestamp)

                if timestamp:
                    demand = float(row[demand_col])
                    result.append((timestamp, demand))
            except (ValueError, TypeError) as e:
                logger.warning(f"Failed to parse Unix timestamp: {row[timestamp_col]} - {str(e)}")

        return result, unit


class DailyParser(BaseParser):
    """
    Parser for daily data
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse daily data

        Args:
            data: DataFrame with a Date column

        Returns:
            List of (timestamp, demand) tuples with 24 entries per day
        """
        result = []

        # Find date column
        date_cols = [col for col in data.columns if 'date' in col.lower()]
        if not date_cols:
            logger.error("No date column found in daily format")
            return result

        date_col = date_cols[0]

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in daily format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            date_str = str(row[date_col])
            date = self.timestamp_handler.parse_timestamp(date_str)

            if date:
                # Daily data is typically cumulative, so we need to distribute it across 24 hours
                daily_demand = float(row[demand_col])
                hourly_demand = daily_demand / 24.0

                # Create 24 hourly entries
                for hour in range(24):
                    timestamp = self.timestamp_handler.construct_timestamp_from_components(
                        year=date.year, month=date.month, day=date.day, hour=hour
                    )
                    result.append((timestamp, hourly_demand))

        return result, unit


class SubHourlyParser(BaseParser):
    """
    Parser for sub-hourly data
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse sub-hourly data

        Args:
            data: DataFrame with a timestamp column with sub-hourly resolution

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find timestamp column
        timestamp_cols = [col for col in data.columns if 'time' in col.lower() or 'date' in col.lower()]
        if not timestamp_cols:
            logger.error("No timestamp column found in sub-hourly format")
            return result

        timestamp_col = timestamp_cols[0]

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in sub-hourly format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            timestamp_str = str(row[timestamp_col])
            timestamp = self.timestamp_handler.parse_timestamp(timestamp_str)

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))

        return result, unit


class SeparateDateTimeParser(BaseParser):
    """
    Parser for data with separate Date and Time columns
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse data with separate Date and Time columns

        Args:
            data: DataFrame with separate Date and Time columns

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Verify that Date and Time columns exist
        if 'Date' not in data.columns or 'Time' not in data.columns:
            logger.error("Date or Time column not found in separate_date_time format")
            return result, 'unknown'

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in separate_date_time format")
            return result, 'unknown'

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            # Combine date and time into a single string
            date_str = str(row['Date'])
            time_str = str(row['Time'])
            combined_str = f"{date_str} {time_str}"

            # Parse the combined timestamp
            timestamp = self.timestamp_handler.parse_timestamp(combined_str)

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))
            else:
                logger.warning(f"Failed to parse timestamp: {combined_str}")

        return result, unit


class NonUniformParser(BaseParser):
    """
    Parser for non-uniform data
    """

    def parse(self, data: pd.DataFrame) -> List[Tuple[datetime, float]]:
        """
        Parse non-uniform data

        Args:
            data: DataFrame with a timestamp column with non-uniform intervals

        Returns:
            List of (timestamp, demand) tuples
        """
        result = []

        # Find timestamp column
        timestamp_cols = [col for col in data.columns if 'time' in col.lower() or 'date' in col.lower()]
        if not timestamp_cols:
            logger.error("No timestamp column found in non-uniform format")
            return result

        timestamp_col = timestamp_cols[0]

        # Find demand column
        demand_col = self._find_demand_column(data.columns)
        if not demand_col:
            logger.error("No demand column found in non-uniform format")
            return result

        # Extract unit
        unit = self._extract_demand_unit(demand_col)

        for _, row in data.iterrows():
            timestamp_str = str(row[timestamp_col])
            timestamp = self.timestamp_handler.parse_timestamp(timestamp_str)

            if timestamp:
                demand = float(row[demand_col])
                result.append((timestamp, demand))

        return result, unit


# Factory function to get the appropriate parser
def get_parser(format_type: str, detected_format: str = None) -> BaseParser:
    """
    Get the appropriate parser for a given format type

    Args:
        format_type: The format type identifier
        detected_format: Detected date format ('DMY', 'MDY', 'YMD', or None)

    Returns:
        Parser instance
    """
    parsers = {
        'day_hour': DayHourParser(detected_format=detected_format),
        'timestamp': TimestampParser(detected_format=detected_format),
        'year_month_day_hour': YearMonthDayHourParser(detected_format=detected_format),
        'json_timestamp': JsonTimestampParser(detected_format=detected_format),
        'unix_timestamp': UnixTimestampParser(detected_format=detected_format),
        'daily': DailyParser(detected_format=detected_format),
        'sub_hourly': SubHourlyParser(detected_format=detected_format),
        'non_uniform': NonUniformParser(detected_format=detected_format),
        'separate_date_time': SeparateDateTimeParser(detected_format=detected_format),
    }

    return parsers.get(format_type, TimestampParser(detected_format=detected_format))  # Default to TimestampParser
