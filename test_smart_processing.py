#!/usr/bin/env python3
"""
Test the smart processing implementation
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_smart_mode():
    """Test smart mode with the problematic file"""
    print("🧠 TESTING SMART PROCESSING MODE")
    print("=" * 60)
    
    input_file = "manyata 13 1.xlsx"
    output_file = "test_smart_mode.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test smart mode (default)
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--smart_mode",  # Use smart processing
        "--output_file", output_file,
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Smart processing completed: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(6, len(lines))):
                print(f"   {lines[i].strip()}")
            
            # Analyze the results
            analysis_results = []
            
            # 1. Check year
            if len(lines) > 1:
                first_line = lines[1].strip()
                if "2023" in first_line:
                    print(f"   ✅ YEAR: Correct (2023)")
                    analysis_results.append("year_correct")
                elif "2025" in first_line or "/25 " in first_line:
                    print(f"   ❌ YEAR: Wrong (2025)")
                else:
                    print(f"   ⚠️ YEAR: Unclear format")
            
            # 2. Check data completeness decision
            data_count = len(lines) - 1
            if data_count > 8000:
                print(f"   📊 DECISION: Completed data ({data_count} points)")
                analysis_results.append("data_completed")
            elif data_count < 1000:
                print(f"   📊 DECISION: Preserved original ({data_count} points)")
                analysis_results.append("data_preserved")
            else:
                print(f"   📊 DECISION: Unclear ({data_count} points)")
            
            # 3. Check for smart processing messages
            if "DATA ANALYSIS RESULTS" in result.stdout:
                print(f"   ✅ ANALYSIS: Smart analysis was performed")
                analysis_results.append("analysis_performed")
            
            if "SMART PRESERVATION" in result.stdout:
                print(f"   ✅ MODE: Smart preservation chosen")
                analysis_results.append("smart_preservation")
            elif "SMART COMPLETION" in result.stdout:
                print(f"   ✅ MODE: Smart completion chosen")
                analysis_results.append("smart_completion")
            
            # 4. Check for original vs generated marking
            if "original + " in result.stdout and "generated" in result.stdout:
                print(f"   ✅ MARKING: Original vs generated data marked")
                analysis_results.append("data_marked")
            
            print(f"\n🎯 SMART PROCESSING ASSESSMENT:")
            print(f"   Success indicators: {len(analysis_results)}/5")
            print(f"   Indicators found: {analysis_results}")
            
            if len(analysis_results) >= 3:
                print(f"   ✅ SMART PROCESSING WORKING!")
                return True
            else:
                print(f"   ❌ SMART PROCESSING NEEDS IMPROVEMENT")
                return False
        else:
            print(f"❌ Smart processing failed")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Smart processing timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_different_modes():
    """Test different processing modes"""
    print(f"\n🔧 TESTING DIFFERENT PROCESSING MODES")
    print("=" * 60)
    
    input_file = "manyata 13 1.xlsx"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    modes = [
        ("--preserve_data", "Forced Preservation"),
        ("--force_complete", "Forced Completion"),
        ("--smart_mode", "Smart Mode (default)")
    ]
    
    results = {}
    
    for flag, description in modes:
        print(f"\n🔧 Testing {description}:")
        
        output_file = f"test_{flag.replace('--', '')}.csv"
        
        cmd = [
            "python3", "main_s3.py",
            "--file", input_file,
            flag,
            "--output_file", output_file,
            "--overwrite"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    lines = f.readlines()
                
                data_count = len(lines) - 1
                print(f"   ✅ {description}: {data_count} points")
                
                # Check for mode-specific messages
                if "PRESERVATION" in result.stdout:
                    print(f"      Mode: Preservation")
                elif "COMPLETION" in result.stdout:
                    print(f"      Mode: Completion")
                elif "SMART" in result.stdout:
                    print(f"      Mode: Smart")
                
                results[flag] = {
                    "success": True,
                    "data_count": data_count,
                    "output_file": output_file
                }
            else:
                print(f"   ❌ {description}: Failed")
                results[flag] = {"success": False}
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ {description}: Timed out")
            results[flag] = {"success": False}
        except Exception as e:
            print(f"   ❌ {description}: Error - {e}")
            results[flag] = {"success": False}
    
    # Compare results
    print(f"\n📊 MODE COMPARISON:")
    for flag, description in modes:
        if flag in results and results[flag]["success"]:
            data_count = results[flag]["data_count"]
            print(f"   {description}: {data_count} points")
        else:
            print(f"   {description}: Failed")
    
    return True

def show_smart_processing_benefits():
    """Show the benefits of smart processing"""
    print(f"\n🎯 SMART PROCESSING BENEFITS")
    print("=" * 60)
    
    print(f"🧠 INTELLIGENT DECISION MAKING:")
    print(f"   • Analyzes data completeness automatically")
    print(f"   • Detects gaps and missing periods")
    print(f"   • Chooses best processing strategy")
    print(f"   • No user configuration needed")
    
    print(f"\n📊 COMPLETENESS ANALYSIS:")
    print(f"   • Counts data points vs expected (8760/8784)")
    print(f"   • Detects time gaps in the data")
    print(f"   • Calculates completeness ratio")
    print(f"   • Identifies leap year requirements")
    
    print(f"\n🔄 PROCESSING STRATEGIES:")
    print(f"   • PRESERVE: For complete data (≥95% complete, no gaps)")
    print(f"   • COMPLETE: For incomplete data (<95% or has gaps)")
    print(f"   • MARK: Original vs generated data clearly marked")
    print(f"   • TRANSPARENT: User knows what was done")
    
    print(f"\n✅ BUSINESS VALUE:")
    print(f"   • Complete data: Preserved exactly (user trust)")
    print(f"   • Incomplete data: Completed intelligently (usable)")
    print(f"   • Always usable: Output works for planning/analysis")
    print(f"   • Transparent: User knows what's original vs generated")

def main():
    """Run all smart processing tests"""
    print("🧠 TESTING SMART PROCESSING IMPLEMENTATION")
    print("=" * 70)
    
    success = True
    
    # Test 1: Smart mode
    if not test_smart_mode():
        success = False
    
    # Test 2: Different modes
    if not test_different_modes():
        success = False
    
    # Show benefits
    show_smart_processing_benefits()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ SMART PROCESSING IMPLEMENTED!")
        print("\n🎯 Key Features:")
        print("  • Automatic data completeness analysis")
        print("  • Intelligent preserve vs complete decision")
        print("  • Original vs generated data marking")
        print("  • User override options available")
        print("  • Transparent processing decisions")
        print("\n🚀 Ready to apply same logic to API!")
    else:
        print("❌ SMART PROCESSING NEEDS WORK!")
        print("Check the test results above for issues.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
