#!/usr/bin/env python3
"""
Test the fixes for year conversion and data preservation
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_year_fix():
    """Test that year conversion is fixed"""
    print("🔧 TESTING YEAR CONVERSION FIX")
    print("=" * 50)
    
    input_file = "manyata 13 1.xlsx"
    output_file = "test_year_fix.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test with forced preservation
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--force_preserve",  # Use the new forced preservation
        "--output_file", output_file,
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Processing completed: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(6, len(lines))):
                print(f"   {lines[i].strip()}")
            
            # Check the year
            if len(lines) > 1:
                first_line = lines[1].strip()
                if "2023" in first_line:
                    print(f"   ✅ YEAR FIX SUCCESSFUL: Shows 2023 (correct)")
                    year_fixed = True
                elif "2025" in first_line or "/25 " in first_line:
                    print(f"   ❌ YEAR STILL WRONG: Shows 2025 instead of 2023")
                    year_fixed = False
                else:
                    print(f"   ⚠️ UNCLEAR: Year format not recognized")
                    year_fixed = False
            else:
                print(f"   ❌ No data to check year")
                year_fixed = False
            
            # Check data preservation
            if len(lines) > 8000:
                print(f"   ✅ DATA PRESERVATION: Full year data ({len(lines)-1} points)")
                preservation_works = True
            else:
                print(f"   ❌ DATA PRESERVATION FAILED: Only {len(lines)-1} points")
                preservation_works = False
            
            return year_fixed and preservation_works
        else:
            print(f"❌ Processing failed")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Processing timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def compare_with_problematic_output():
    """Compare with the problematic my_results7.csv"""
    print(f"\n📊 COMPARING WITH PROBLEMATIC OUTPUT")
    print("=" * 50)
    
    files_to_compare = [
        ("my_results7.csv", "Problematic output (WRONG)"),
        ("test_year_fix.csv", "Fixed output (should be CORRECT)")
    ]
    
    for filename, description in files_to_compare:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            print(f"\n📁 {description}:")
            print(f"   File: {filename}")
            print(f"   Lines: {len(lines)}")
            
            if len(lines) > 1:
                first_line = lines[1].strip()
                print(f"   First data: {first_line}")
                
                # Check year
                if "2023" in first_line:
                    print(f"   Year: ✅ 2023 (correct)")
                elif "2025" in first_line or "/25 " in first_line:
                    print(f"   Year: ❌ 2025 (wrong)")
                else:
                    print(f"   Year: ⚠️ Unknown format")
                
                # Check data count
                if len(lines) > 8000:
                    print(f"   Data: ✅ Full year ({len(lines)-1} points)")
                else:
                    print(f"   Data: ❌ Partial ({len(lines)-1} points)")
        else:
            print(f"\n⚠️ {description}: File not found ({filename})")

def show_fix_summary():
    """Show what was fixed"""
    print(f"\n🔧 FIXES APPLIED")
    print("=" * 50)
    
    print(f"✅ FIX 1: Year Conversion")
    print(f"   • Changed date format from %m/%d/%y to %m/%d/%Y")
    print(f"   • This prevents 2023 → 2025 conversion")
    print(f"   • Now outputs 4-digit years (01/01/2023)")
    
    print(f"\n✅ FIX 2: Forced Data Preservation")
    print(f"   • Added --force_preserve flag (default: True)")
    print(f"   • Ensures preservation mode is always used")
    print(f"   • Disables all data modification functions")
    
    print(f"\n✅ FIX 3: Preservation Logic")
    print(f"   • Fixed the preservation mode logic")
    print(f"   • Now properly disables outlier detection, scaling, etc.")
    print(f"   • Preserves original values exactly")
    
    print(f"\n🎯 EXPECTED RESULTS:")
    print(f"   • Year: 2023 (not 2025)")
    print(f"   • Values: Original values preserved")
    print(f"   • Count: 8760 points for 2023 (full year)")
    print(f"   • Processing: Fast (no modifications)")

def main():
    """Run all tests for the fixes"""
    print("🔧 TESTING CRITICAL FIXES")
    print("=" * 70)
    
    success = test_year_fix()
    compare_with_problematic_output()
    show_fix_summary()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ FIXES SUCCESSFUL!")
        print("\n🎯 Results:")
        print("  • Year conversion fixed (2023 not 2025)")
        print("  • Data preservation working")
        print("  • Full year data generated")
        print("  • Ready to fix API with same logic")
    else:
        print("❌ FIXES FAILED!")
        print("\n🔍 Issues:")
        print("  • Check the test output above")
        print("  • Year might still be wrong")
        print("  • Preservation might not be working")
        print("  • Need further investigation")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
