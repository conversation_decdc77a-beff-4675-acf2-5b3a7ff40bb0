# Demand Data Pipeline API

This API provides an endpoint for processing demand data files from S3 using the Demand Data Pipeline.

## Features

- Process demand data files from S3
- Save processed data to S3
- Maintain the same filename structure (input.csv -> input.json)
- RESTful API with FastAPI
- Deployment with Gun<PERSON> and Nginx

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd demand-api
   ```

2. Create a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file with your AWS credentials and API key:
   ```
   BUCKET_NAME=demandprofiles
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   REGION_NAME=us-west-2
   OUTPUT_BUCKET=demandjsons

   # API Authentication
   API_KEY=your-secret-api-key-here
   ```

## Running Locally

Run the API locally with Uvicorn:

```bash
uvicorn app.main:app --reload
```

The API will be available at http://localhost:8000.

## API Documentation

Once the API is running, you can access the interactive API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Endpoints

### POST /process-file

Process a demand data file from S3 and save the output as CSV with Timestamp and Demand columns.

**Request Body:**
```json
{
  "input_file": "path/to/input.csv",
  "output_path": "path/to/output/"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully processed file: path/to/input.csv",
  "output_file": "path/to/output/input.csv",
  "generated_points": 8760
}
```

The output CSV file will have two columns:
- Timestamp: Combined date and time (e.g., "01/01/25 18:00")
- Demand: The demand value

### POST /process-metadata

Process metadata for a demand data file and save it as a JSON file with the same filename but with a .json extension.

**Request Body:**
```json
{
  "input_file": "path/to/input.csv",
  "output_path": "path/to/output/",
  "country_code": "IND",  // Can be 2-letter code (IN), 3-letter code (IND), or country name (India)
  "industry_type": "Commercial",
  "sub_industry_type": "Data-center"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully processed metadata for: path/to/input.csv",
  "output_file": "path/to/output/input.json",
  "timezone": "IST"
}
```

The output JSON file will contain:
```json
{
  "timezone": "IST",
  "industry_type": "Commercial",
  "sub_industry_type": "Data-center"
}
```

The API automatically determines the timezone based on the provided country code.

## Deployment

### EC2 Deployment

1. Launch an EC2 instance with Ubuntu
2. Clone the repository to the instance
3. Run the deployment script:
   ```bash
   ./deploy.sh
   ```

### Manual Deployment

1. Install dependencies:
   ```bash
   sudo apt update
   sudo apt install -y python3-pip python3-venv nginx
   ```

2. Set up the virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. Set up the systemd service:
   ```bash
   sudo cp demand-api.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable demand-api
   sudo systemctl start demand-api
   ```

4. Set up Nginx:
   ```bash
   sudo cp nginx-demand-api /etc/nginx/sites-available/demand-api
   sudo ln -s /etc/nginx/sites-available/demand-api /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

5. Set up SSL with Certbot (optional):
   ```bash
   sudo apt install -y certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

## Testing

### Testing the API Directly

You can test the API processing functions directly without starting the server:

#### Testing the demand data processing:

```bash
python test_local.py [input_file] [output_path]
```

For example:
```bash
python test_local.py format8_unix.csv output/
```

#### Testing the metadata processing:

```bash
python test_local_metadata.py [input_file] [output_path] [country_code] [industry_type] [sub_industry_type]
```

For example:
```bash
python test_local_metadata.py format8_unix.csv output/ IN Commercial Data-center
```

### Testing the API with curl

Once the API is running, you can test it using curl with your API key:

#### Testing the process-file endpoint:

```bash
curl -X POST "http://localhost:8000/process-file" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key-here" \
  -d '{
    "input_file": "format8_unix.csv",
    "output_path": "output/"
  }'
```

You can also use the full S3 URL format:

```bash
curl -X POST "http://localhost:8000/process-file" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key-here" \
  -d '{
    "input_file": "s3://demandprofiles/format8_unix.csv",
    "output_path": "s3://demandjsons/output/"
  }'
```

#### Testing the process-metadata endpoint:

```bash
curl -X POST "http://localhost:8000/process-metadata" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key-here" \
  -d '{
    "input_file": "format8_unix.csv",
    "output_path": "output/",
    "country_code": "IN",
    "industry_type": "Commercial",
    "sub_industry_type": "Data-center"
  }'
```

With full S3 URL format:

```bash
curl -X POST "http://localhost:8000/process-metadata" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key-here" \
  -d '{
    "input_file": "s3://demandprofiles/format8_unix.csv",
    "output_path": "s3://demandjsons/output/",
    "country_code": "US",
    "industry_type": "Industrial",
    "sub_industry_type": "Manufacturing"
  }'
```

#### Data Expansion

The API always expands data to a complete year. This means that any missing data points will be generated to create a full year of hourly data.

#### Generated Data

The API internally tracks which data points are generated to fill gaps in the input data, but this information is not included in the output CSV file. The output will always contain just the Timestamp and Demand columns.

If you don't include the API key or use an invalid key, you'll get a 401 Unauthorized response.

### Using the Interactive API Documentation

You can also test the API using the interactive API documentation at:
- http://localhost:8000/docs (Swagger UI)
- http://localhost:8000/redoc (ReDoc)
