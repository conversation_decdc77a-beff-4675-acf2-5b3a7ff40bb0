#!/usr/bin/env python3
"""
Debug script to check why preservation mode is not working
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_preservation_modes():
    """Test different preservation modes"""
    print("🔍 DEBUGGING PRESERVATION MODES")
    print("=" * 60)
    
    input_file = "manyata 13 1.xlsx"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test 1: Explicit preservation mode
    print(f"🔧 Test 1: Explicit --preserve_data flag")
    
    cmd1 = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--preserve_data",  # Explicit flag
        "--output_file", "test_explicit_preserve.csv",
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd1)}")
    
    try:
        result1 = subprocess.run(cmd1, capture_output=True, text=True, timeout=60)
        
        print(f"Return code: {result1.returncode}")
        print(f"STDOUT: {result1.stdout}")
        if result1.stderr:
            print(f"STDERR: {result1.stderr}")
        
        if result1.returncode == 0 and os.path.exists("test_explicit_preserve.csv"):
            with open("test_explicit_preserve.csv", 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Explicit preservation: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(4, len(lines))):
                print(f"   {lines[i].strip()}")
        else:
            print(f"❌ Explicit preservation failed")
            
    except subprocess.TimeoutExpired:
        print(f"❌ Explicit preservation timed out")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Default mode (should be preservation)
    print(f"\n🔧 Test 2: Default mode (should preserve)")
    
    cmd2 = [
        "python3", "main_s3.py",
        "--file", input_file,
        # No explicit flags - should default to preservation
        "--output_file", "test_default_mode.csv",
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd2)}")
    
    try:
        result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=60)
        
        print(f"Return code: {result2.returncode}")
        print(f"STDOUT: {result2.stdout}")
        if result2.stderr:
            print(f"STDERR: {result2.stderr}")
        
        if result2.returncode == 0 and os.path.exists("test_default_mode.csv"):
            with open("test_default_mode.csv", 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Default mode: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(4, len(lines))):
                print(f"   {lines[i].strip()}")
        else:
            print(f"❌ Default mode failed")
            
    except subprocess.TimeoutExpired:
        print(f"❌ Default mode timed out")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Explicit modification mode
    print(f"\n🔧 Test 3: Explicit --allow_modifications flag")
    
    cmd3 = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--allow_modifications",  # Explicit modifications
        "--output_file", "test_explicit_modify.csv",
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd3)}")
    
    try:
        result3 = subprocess.run(cmd3, capture_output=True, text=True, timeout=60)
        
        print(f"Return code: {result3.returncode}")
        print(f"STDOUT: {result3.stdout}")
        if result3.stderr:
            print(f"STDERR: {result3.stderr}")
        
        if result3.returncode == 0 and os.path.exists("test_explicit_modify.csv"):
            with open("test_explicit_modify.csv", 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Explicit modifications: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(4, len(lines))):
                print(f"   {lines[i].strip()}")
        else:
            print(f"❌ Explicit modifications failed")
            
    except subprocess.TimeoutExpired:
        print(f"❌ Explicit modifications timed out")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return True

def analyze_my_results7():
    """Analyze the problematic my_results7.csv"""
    print(f"\n📊 ANALYZING PROBLEMATIC OUTPUT")
    print("=" * 60)
    
    if os.path.exists("my_results7.csv"):
        with open("my_results7.csv", 'r') as f:
            lines = f.readlines()
        
        print(f"File: my_results7.csv")
        print(f"Lines: {len(lines)}")
        print(f"Sample data:")
        for i in range(1, min(6, len(lines))):
            print(f"   {lines[i].strip()}")
        
        # Check what's wrong
        print(f"\n❌ ISSUES IDENTIFIED:")
        print(f"   • Year: Shows 25 (2025) instead of 23 (2023)")
        print(f"   • Values: Modified (55.78, 53.01) instead of preserved")
        print(f"   • Count: Only {len(lines)-1} points instead of 8760")
        print(f"   • This indicates preservation mode was NOT used")
        
        # Check if this matches any of our test outputs
        test_files = [
            "test_explicit_preserve.csv",
            "test_default_mode.csv", 
            "test_explicit_modify.csv"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                with open(test_file, 'r') as f:
                    test_lines = f.readlines()
                
                if len(test_lines) == len(lines):
                    # Compare first few lines
                    matches = 0
                    for i in range(1, min(4, len(lines))):
                        if lines[i].strip() == test_lines[i].strip():
                            matches += 1
                    
                    if matches >= 2:
                        print(f"   ✅ my_results7.csv matches {test_file}")
                        if "modify" in test_file:
                            print(f"      → This confirms preservation mode was NOT used")
                        elif "preserve" in test_file:
                            print(f"      → This suggests preservation mode has a bug")
    else:
        print(f"❌ my_results7.csv not found")

def show_fix_needed():
    """Show what needs to be fixed"""
    print(f"\n🔧 FIXES NEEDED")
    print("=" * 60)
    
    print(f"❌ ISSUE 1: Year Conversion")
    print(f"   • Input file: manyata 13 1.xlsx (should be 2023)")
    print(f"   • Output: 01/01/25 (2025) - WRONG!")
    print(f"   • Fix: Check timestamp parsing for 2-digit years")
    
    print(f"\n❌ ISSUE 2: Data Preservation Not Working")
    print(f"   • Values are being modified")
    print(f"   • Only partial data (746 vs 8760 points)")
    print(f"   • Fix: Ensure preservation mode actually disables modifications")
    
    print(f"\n❌ ISSUE 3: Default Behavior")
    print(f"   • main_s3.py should preserve data by default")
    print(f"   • Currently it's modifying data")
    print(f"   • Fix: Ensure default=True for preserve_data works correctly")

def main():
    """Run all debugging tests"""
    print("🔍 DEBUGGING PRESERVATION MODE FAILURES")
    print("=" * 70)
    
    success = test_preservation_modes()
    analyze_my_results7()
    show_fix_needed()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ DEBUGGING COMPLETED!")
        print("\nNext steps:")
        print("  1. Fix year conversion (2023 not 2025)")
        print("  2. Fix preservation mode (values being modified)")
        print("  3. Ensure default behavior preserves data")
        print("  4. Test with the problematic file")
    else:
        print("❌ DEBUGGING FAILED!")
        print("Check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
