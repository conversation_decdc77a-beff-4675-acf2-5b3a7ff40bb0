import json
import csv
import os
import pandas as pd
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_json_to_csv(json_data: List[Dict[str, Any]], output_file: str = 'output.csv') -> str:
    """
    Convert JSON data to CSV format with Timestamp and Demand columns.

    Args:
        json_data: List of dictionaries with Date, Time, and Demand fields
        output_file: Name of the output CSV file

    Returns:
        Path to the generated CSV file
    """
    try:
        # Create a list to store the converted data
        csv_data = []

        # Process each item in the JSON data
        for item in json_data:
            # Extract the fields
            date = item.get('Date', '')
            time = item.get('Time', '')
            demand = item.get('Demand', 0.0)

            # Combine Date and Time to create Timestamp
            timestamp = f"{date} {time}"

            # Add to the CSV data
            csv_data.append({
                'Timestamp': timestamp,
                'Demand': demand
            })

        # Convert to DataFrame for easier CSV writing
        df = pd.DataFrame(csv_data)

        # Sort by timestamp for better organization
        df['Timestamp_sort'] = pd.to_datetime(df['Timestamp'], format='%m/%d/%y %H:%M')
        df = df.sort_values('Timestamp_sort')
        df = df.drop(columns=['Timestamp_sort'])

        # Write to CSV
        df.to_csv(output_file, index=False)

        logger.info(f"Successfully converted JSON to CSV: {output_file}")
        return output_file

    except Exception as e:
        logger.error(f"Error converting JSON to CSV: {str(e)}")
        return None

def convert_json_file_to_csv(json_file: str, output_file: str = None) -> str:
    """
    Convert a JSON file to CSV format.

    Args:
        json_file: Path to the JSON file
        output_file: Name of the output CSV file (if None, uses the same name with .csv extension)

    Returns:
        Path to the generated CSV file
    """
    try:
        # Determine output file name if not provided
        if output_file is None:
            base_name = os.path.splitext(json_file)[0]
            output_file = f"{base_name}.csv"

        # Read the JSON file
        with open(json_file, 'r') as f:
            json_data = json.load(f)

        # Convert to CSV
        return convert_json_to_csv(json_data, output_file)

    except Exception as e:
        logger.error(f"Error converting JSON file to CSV: {str(e)}")
        return None

def get_csv_as_string(json_data: List[Dict[str, Any]]) -> str:
    """
    Convert JSON data to CSV format and return as a string.

    Args:
        json_data: List of dictionaries with Date, Time, and Demand fields

    Returns:
        CSV data as a string
    """
    try:
        # Create a list to store the converted data
        csv_data = []

        # Process each item in the JSON data
        for item in json_data:
            # Extract the fields
            date = item.get('Date', '')
            time = item.get('Time', '')
            demand = item.get('Demand', 0.0)

            # Combine Date and Time to create Timestamp
            timestamp = f"{date} {time}"

            # Add to the CSV data
            csv_data.append({
                'Timestamp': timestamp,
                'Demand': demand
            })

        # Convert to DataFrame for easier CSV writing
        df = pd.DataFrame(csv_data)

        # Sort by timestamp for better organization
        df['Timestamp_sort'] = pd.to_datetime(df['Timestamp'], format='%m/%d/%y %H:%M')
        df = df.sort_values('Timestamp_sort')
        df = df.drop(columns=['Timestamp_sort'])

        # Convert to CSV string
        csv_string = df.to_csv(index=False)

        return csv_string

    except Exception as e:
        logger.error(f"Error converting JSON to CSV string: {str(e)}")
        return ""

if __name__ == "__main__":
    # Example usage
    import sys

    if len(sys.argv) > 1:
        json_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None

        result = convert_json_file_to_csv(json_file, output_file)

        if result:
            print(f"Successfully converted {json_file} to {result}")
        else:
            print(f"Failed to convert {json_file}")
    else:
        print("Usage: python json_to_csv_converter.py <json_file> [output_file]")
