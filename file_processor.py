import os
import json
import csv
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from date_format_detector import DateFormatDetector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileProcessor:
    """
    Handles file detection, reading, and format identification
    """

    def __init__(self, directory_path: str = None):
        """
        Initialize the FileProcessor

        Args:
            directory_path: Path to the directory containing input files
        """
        self.directory_path = directory_path or os.getcwd()
        self.supported_extensions = ['.csv', '.json', '.txt', '.xlsx']
        self.format_detector = DateFormatDetector()

    def get_files(self) -> List[str]:
        """
        Get all supported files in the directory

        Returns:
            List of file paths
        """
        files = []
        for file in os.listdir(self.directory_path):
            file_path = os.path.join(self.directory_path, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file_path)
                if ext.lower() in self.supported_extensions:
                    files.append(file_path)

        logger.info(f"Found {len(files)} supported files in {self.directory_path}")
        return files

    def read_file(self, file_path: str) -> Tuple[Any, str]:
        """
        Read a file and return its content and format

        Args:
            file_path: Path to the file

        Returns:
            Tuple of (file_content, file_format)
        """
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        try:
            if ext == '.csv':
                # Try to read as pandas DataFrame
                df = pd.read_csv(file_path)
                logger.info(f"Successfully read CSV file: {file_path}")
                return df, 'csv'

            elif ext == '.json':
                with open(file_path, 'r') as f:
                    data = json.load(f)
                logger.info(f"Successfully read JSON file: {file_path}")
                return data, 'json'

            elif ext == '.txt':
                with open(file_path, 'r') as f:
                    content = f.read()
                logger.info(f"Successfully read TXT file: {file_path}")
                return content, 'txt'

            elif ext == '.xlsx':
                # Try to read as pandas DataFrame from Excel
                try:
                    df = pd.read_excel(file_path, sheet_name=0)
                    logger.info(f"Successfully read Excel file: {file_path}")
                    return df, 'excel'
                except Exception as e:
                    logger.error(f"Error reading Excel file {file_path}: {str(e)}")
                    return None, None

            else:
                logger.warning(f"Unsupported file extension: {ext}")
                return None, None

        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            return None, None

    def detect_date_format(self, data: Any) -> str:
        """
        Detect the date format in the data

        Args:
            data: The data to analyze

        Returns:
            Detected format ('DMY', 'MDY', 'YMD', or 'unknown')
        """
        # Extract dates from the data
        dates = self.format_detector.extract_dates(data)

        if not dates:
            logger.warning("No dates found for format detection")
            return 'unknown'

        # Detect the format
        detected_format = self.format_detector.detect_format(dates)
        logger.info(f"Detected date format: {detected_format}")

        return detected_format

    def identify_format(self, data: Any) -> str:
        """
        Identify the format of the data with validation and recovery

        Args:
            data: The data to identify

        Returns:
            Format identifier string
        """
        if isinstance(data, pd.DataFrame):
            # Validate DataFrame
            if data.empty:
                logger.error("DataFrame is empty")
                raise ValueError("Input data is empty")

            columns = data.columns.tolist()
            logger.info(f"Analyzing DataFrame with columns: {columns}")

            # Validate minimum requirements
            if len(columns) < 2:
                logger.error(f"DataFrame has only {len(columns)} column(s), need at least 2")
                raise ValueError(f"Data must have at least 2 columns (timestamp and demand), found {len(columns)}")

            # Check for day-hour format
            hour_columns = [col for col in columns if col.strip() == 'Hour']
            if ('Day' in columns and (hour_columns or 'Hour' in columns)) or \
               ('Date' in columns and (hour_columns or 'Hour' in columns)):
                logger.info("Identified day-hour format")
                return 'day_hour'

            # Check for separate Date and Time columns format
            if 'Date' in columns and 'Time' in columns:
                logger.info("Identified separate date-time format")
                return 'separate_date_time'

            # Check for timestamp format
            timestamp_cols = [col for col in columns if 'time' in col.lower() or 'date' in col.lower()]
            if timestamp_cols:
                logger.info(f"Found timestamp columns: {timestamp_cols}")

                # Validate timestamp column data
                timestamp_col = timestamp_cols[0]
                non_null_count = data[timestamp_col].notna().sum()
                if non_null_count == 0:
                    logger.error(f"Timestamp column '{timestamp_col}' contains no valid data")
                    raise ValueError(f"Timestamp column '{timestamp_col}' is empty or contains only null values")

                logger.info(f"Timestamp column '{timestamp_col}' has {non_null_count}/{len(data)} valid entries")

                # Check for sub-hourly data
                if data[timestamp_col].dtype == 'object':
                    try:
                        sample_time = str(data[timestamp_col].iloc[0])
                        if ':' in sample_time:
                            if sample_time.count(':') == 2:  # HH:MM:SS
                                minutes = [int(str(t).split(':')[1]) for t in data[timestamp_col] if ':' in str(t)]
                                if any(m != 0 for m in minutes):
                                    logger.info("Identified sub-hourly format (seconds precision)")
                                    return 'sub_hourly'
                            elif sample_time.count(':') == 1:  # HH:MM
                                minutes = [int(str(t).split(':')[1]) for t in data[timestamp_col] if ':' in str(t)]
                                if any(m != 0 for m in minutes):
                                    logger.info("Identified sub-hourly format (minute precision)")
                                    return 'sub_hourly'
                    except Exception as e:
                        logger.warning(f"Error analyzing timestamp format: {str(e)}")

                # Check for date-only format (multiple entries per date)
                if 'Date' in columns and len(columns) == 2:
                    # Check if we have multiple entries for the same date
                    if data[timestamp_col].dtype == 'object':
                        # Count unique dates vs total entries
                        unique_dates = data[timestamp_col].nunique()
                        total_entries = len(data)

                        if total_entries > unique_dates:
                            entries_per_date = total_entries / unique_dates
                            logger.info(f"Found {total_entries} entries for {unique_dates} unique dates ({entries_per_date:.1f} entries per date)")

                            # If we have approximately 24 entries per date, it's likely hourly data without time
                            if 20 <= entries_per_date <= 25:  # Allow some tolerance
                                logger.info("Identified date-only format (hourly data without time components)")
                                return 'date_only'

                # Check for daily data - but verify it's actually daily, not hourly
                if 'Date' in columns and len(columns) == 2:
                    # Check if the timestamps have time components (not just dates)
                    if data[timestamp_col].dtype == 'datetime64[ns]':
                        # Check if we have time components (hours/minutes)
                        sample_times = data[timestamp_col].head(10)
                        has_time_component = any(
                            dt.hour != 0 or dt.minute != 0 or dt.second != 0
                            for dt in sample_times if pd.notna(dt)
                        )
                        if has_time_component:
                            logger.info("Identified timestamp format (has time components)")
                            return 'timestamp'
                        else:
                            logger.info("Identified daily format (date only)")
                            return 'daily'
                    else:
                        logger.info("Identified daily format")
                        return 'daily'

                # Check for unix timestamp
                if data[timestamp_col].dtype == 'int64' or data[timestamp_col].dtype == 'float64':
                    logger.info("Identified unix timestamp format")
                    return 'unix_timestamp'

                logger.info("Identified standard timestamp format")
                return 'timestamp'

            # Check for year-month-day-hour format
            if 'Year' in columns and 'Month' in columns and 'Day' in columns and 'Hour' in columns:
                return 'year_month_day_hour'

            # Check for non-uniform data
            if len(data) < 24 and 'Time' in ''.join(columns):
                return 'non_uniform'

        elif isinstance(data, list) and isinstance(data[0], dict):
            # JSON format
            keys = data[0].keys()
            if 'timestamp' in keys and ('demand_kW' in keys or 'demand' in keys):
                logger.info("Identified JSON timestamp format")
                return 'json_timestamp'

        # Fallback: try to infer format from column names
        logger.warning("Could not identify format using standard methods, attempting fallback analysis")
        return self._fallback_format_identification(data)

    def _fallback_format_identification(self, data: Any) -> str:
        """
        Fallback method to identify format when standard methods fail

        Args:
            data: The data to analyze

        Returns:
            Best guess format identifier
        """
        if isinstance(data, pd.DataFrame):
            columns = [str(col).lower() for col in data.columns]

            # Look for any time-related columns
            time_indicators = ['time', 'date', 'timestamp', 'hour', 'day', 'month', 'year']
            has_time_col = any(indicator in col for col in columns for indicator in time_indicators)

            # Look for demand-related columns
            demand_indicators = ['demand', 'power', 'energy', 'load', 'consumption', 'usage']
            has_demand_col = any(indicator in col for col in columns for indicator in demand_indicators)

            if has_time_col and has_demand_col:
                logger.info("Fallback: detected timestamp format based on column names")
                return 'timestamp'
            elif has_demand_col:
                logger.warning("Fallback: found demand column but no clear timestamp column")
                return 'timestamp'  # Assume timestamp format and let parser handle it

        logger.error("Fallback format identification failed")
        raise ValueError("Unable to identify data format. Please ensure the file contains recognizable timestamp and demand columns.")
