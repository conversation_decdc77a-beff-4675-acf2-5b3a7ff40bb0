import os
import argparse
import json
import logging
import pandas as pd
from typing import Dict, List, Any, Tuple
from dotenv import load_dotenv

from file_processor import FileProcessor
from parsers import get_parser
from data_processor import DataProcessor
from output_generator import OutputGenerator
from llm_handler import LLMHandler
from s3_file_handler import S3FileHandler
from timestamp_handler import TimestampHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(dotenv_path=".env", override=True)

# LLM configuration
LLM_CONFIG = {
    "api_base": "http://192.168.0.152:11435/v1",
    "api_key": "dummy-key",
    "model_name": "gemma3:12b",
    "temperature": 0
}

def analyze_data_completeness(parsed_data: List[Tuple], file_format: str = "", file_name: str = "") -> Dict[str, Any]:
    """
    Analyze data completeness to determine processing strategy

    Args:
        parsed_data: List of (timestamp, demand) tuples
        file_name: Name of the file being processed

    Returns:
        Dictionary with analysis results and recommended action
    """
    if not parsed_data:
        return {
            "action": "error",
            "reason": "No data found",
            "data_points": 0,
            "is_complete": False
        }

    data_points = len(parsed_data)

    # Extract year from first timestamp to determine expected count
    first_timestamp = parsed_data[0][0]
    year = first_timestamp.year

    # Determine if it's a leap year
    import calendar
    is_leap_year = calendar.isleap(year)
    expected_hourly_points = 8784 if is_leap_year else 8760

    # Adjust expectations based on file format
    if file_format == "daily":
        # Daily format: expect ~365 days, but parser expands to hourly
        # So we should expect around expected_hourly_points after parsing
        logger.info(f"📊 DAILY FORMAT: Expecting ~{expected_hourly_points} points after hourly expansion")
        # For daily format, if we have way more than expected, it's likely over-expanded
        if data_points > expected_hourly_points * 2:
            logger.warning(f"📊 DAILY FORMAT: Data appears over-expanded ({data_points} vs {expected_hourly_points} expected)")
            # Treat as needing completion/correction
            expected_hourly_points = data_points  # Adjust expectation to current data
    elif file_format in ["date_only", "day_hour", "year_month_day_hour"]:
        # These formats should already be hourly or close to hourly
        logger.info(f"📊 HOURLY FORMAT ({file_format}): Expecting ~{expected_hourly_points} points")
    else:
        logger.info(f"📊 UNKNOWN FORMAT ({file_format}): Using default hourly expectation")

    # Check for gaps in the data
    timestamps = [ts for ts, _ in parsed_data]
    timestamps.sort()

    # Calculate time differences to detect gaps
    gaps_found = []
    if len(timestamps) > 1:
        for i in range(1, len(timestamps)):
            time_diff = (timestamps[i] - timestamps[i-1]).total_seconds() / 3600  # hours
            if time_diff > 1.5:  # Gap larger than 1.5 hours
                gaps_found.append({
                    "start": timestamps[i-1],
                    "end": timestamps[i],
                    "hours_missing": time_diff - 1
                })

    # Determine completeness
    completeness_ratio = data_points / expected_hourly_points
    has_major_gaps = len(gaps_found) > 0

    # Decision logic - format-aware
    if file_format == "daily":
        # Daily format: if we have reasonable amount of data, preserve it
        # Daily data is typically already processed/aggregated
        if data_points >= expected_hourly_points * 0.8:  # At least 80% of expected hourly data
            action = "preserve"
            reason = f"Daily format data with {data_points} points (sufficient for analysis)"
        else:
            action = "complete"
            reason = f"Daily format data with only {data_points} points (needs completion)"
    else:
        # Hourly format: use original logic
        if completeness_ratio >= 0.95 and not has_major_gaps:
            # Data is essentially complete - preserve it
            action = "preserve"
            reason = f"Data is {completeness_ratio:.1%} complete with no major gaps"
        elif completeness_ratio >= 0.80 and len(gaps_found) <= 3:
            # Data is mostly complete with minor gaps - complete it
            action = "complete"
            reason = f"Data is {completeness_ratio:.1%} complete with {len(gaps_found)} minor gaps"
        elif data_points < expected_hourly_points * 0.5:
            # Very incomplete data - complete it
            action = "complete"
            reason = f"Data is only {completeness_ratio:.1%} complete ({data_points}/{expected_hourly_points} points)"
        else:
            # Moderate incompleteness - complete it
            action = "complete"
            reason = f"Data has {len(gaps_found)} gaps and is {completeness_ratio:.1%} complete"

    return {
        "action": action,
        "reason": reason,
        "data_points": data_points,
        "expected_points": expected_hourly_points,
        "completeness_ratio": completeness_ratio,
        "gaps_found": len(gaps_found),
        "gap_details": gaps_found[:5],  # First 5 gaps for logging
        "is_complete": action == "preserve",
        "year": year,
        "is_leap_year": is_leap_year
    }

def check_demand_units(data: Any, format_type: str) -> str:
    """
    Explicitly check for demand units in the input data

    Args:
        data: The input data
        format_type: The identified format type

    Returns:
        Detected unit string ('MW', 'kW', etc.)
    """
    # Initialize with unknown unit
    detected_unit = 'unknown'

    try:
        if isinstance(data, pd.DataFrame):
            # Check column names for unit information
            for col in data.columns:
                col_upper = col.upper()
                if 'MW' in col_upper or 'MEGAWATT' in col_upper:
                    return 'MW'
                elif any(unit in col_upper for unit in ['KW', 'KWH', 'KILOWATT']):
                    return 'kW'
                elif any(unit in col_upper for unit in ['W', 'WH', 'WATT']) and not any(unit in col_upper for unit in ['KW', 'MW', 'GW']):
                    return 'W'
                elif any(unit in col_upper for unit in ['GW', 'GWH', 'GIGAWATT']):
                    return 'GW'

            # If no unit found in column names, check the data magnitude
            if 'demand' in format_type.lower() or 'energy' in format_type.lower() or 'power' in format_type.lower():
                # Find numeric columns
                numeric_cols = data.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    # Get the last numeric column (likely to be the demand column)
                    demand_col = numeric_cols[-1]
                    # Calculate median value
                    median_value = data[demand_col].median()

                    # Determine unit based on magnitude
                    if 0.1 <= median_value <= 100:
                        return 'MW'  # Likely MW
                    elif 100 <= median_value <= 100000:
                        return 'kW'  # Likely kW
                    elif median_value > 100000:
                        return 'W'   # Likely W
                    elif median_value < 0.1:
                        return 'GW'  # Likely GW

        elif isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
            # Check keys for unit information
            for key in data[0].keys():
                key_upper = key.upper()
                if 'MW' in key_upper or 'MEGAWATT' in key_upper:
                    return 'MW'
                elif any(unit in key_upper for unit in ['KW', 'KWH', 'KILOWATT']):
                    return 'kW'
                elif any(unit in key_upper for unit in ['W', 'WH', 'WATT']) and not any(unit in key_upper for unit in ['KW', 'MW', 'GW']):
                    return 'W'
                elif any(unit in key_upper for unit in ['GW', 'GWH', 'GIGAWATT']):
                    return 'GW'

            # If no unit found in keys, check the data magnitude
            demand_keys = ['demand', 'power', 'energy', 'consumption']
            for key in data[0].keys():
                if any(dk in key.lower() for dk in demand_keys) and isinstance(data[0][key], (int, float)):
                    # Calculate median value
                    values = [item[key] for item in data if key in item and isinstance(item[key], (int, float))]
                    if values:
                        median_value = sorted(values)[len(values) // 2]

                        # Determine unit based on magnitude
                        if 0.1 <= median_value <= 1000:
                            return 'MW'  # Likely MW
                        elif 100 <= median_value <= 100000:
                            return 'kW'  # Likely kW
                        elif median_value > 100000:
                            return 'W'   # Likely W
                        elif median_value < 0.1:
                            return 'GW'  # Likely GW
    except Exception as e:
        logger.warning(f"Error checking demand units: {str(e)}")

    return detected_unit

def process_file(file_path: str, llm_handler: LLMHandler = None, preserve_data: bool = None) -> List[Dict[str, Any]]:
    """
    Process a single local file

    Args:
        file_path: Path to the file
        llm_handler: LLMHandler instance for complex parsing tasks

    Returns:
        List of dictionaries with Date, Time, and Demand fields
    """
    logger.info(f"Processing local file: {file_path}")

    # Initialize components
    file_processor = FileProcessor()
    data_processor = DataProcessor()

    # Read the file
    data, file_format = file_processor.read_file(file_path)

    if data is None:
        logger.error(f"Failed to read file: {file_path}")
        return {}

    return process_data(data, file_format, llm_handler, preserve_data)

def process_s3_file(file_name: str, llm_handler: LLMHandler = None, bucket_name: str = None, preserve_data: bool = None) -> List[Dict[str, Any]]:
    """
    Process a single file from S3

    Args:
        file_name: Name of the file in S3 bucket
        llm_handler: LLMHandler instance for complex parsing tasks
        bucket_name: Optional bucket name to override the one in .env

    Returns:
        List of dictionaries with Date, Time, and Demand fields
    """
    logger.info(f"Processing S3 file: {file_name}")

    # Initialize S3 file handler
    s3_handler = S3FileHandler(bucket_name=bucket_name)
    data_processor = DataProcessor()

    # Read the file from S3
    data, file_format = s3_handler.read_file(file_name)

    if data is None:
        logger.error(f"Failed to read S3 file: {file_name}")
        return {}



    return process_data(data, file_format, llm_handler, preserve_data)

def process_data(data: Any, file_format: str, llm_handler: LLMHandler = None, preserve_data: bool = None) -> List[Dict[str, Any]]:
    """
    Process data that's already loaded

    Args:
        data: The loaded data (DataFrame, dict, etc.)
        file_format: Format of the data ('csv', 'json', 'excel', etc.)
        llm_handler: LLMHandler instance for complex parsing tasks

    Returns:
        List of dictionaries with Date, Time, and Demand fields
    """
    # Initialize components
    file_processor = FileProcessor()
    data_processor = DataProcessor()

    # If the format is 'excel', treat it as a DataFrame (similar to CSV)
    if file_format == 'excel':
        file_format = 'csv'  # Process Excel files the same way as CSV files
        logger.info("Processing Excel file as CSV format")

    # Identify the format
    try:
        format_type = file_processor.identify_format(data)
        logger.info(f"Identified format: {format_type}")

        # APPLY FORMAT DETECTION FIX for known problematic files
        # This ensures API produces same results as local processing
        if format_type == 'daily' and hasattr(data, 'columns') and 'Date' in data.columns:
            # Check if Date column actually has time components (hourly data)
            date_col = data['Date']
            if hasattr(date_col, 'dtype') and 'datetime' in str(date_col.dtype):
                # Check for time components in first few entries
                sample_times = date_col.head(10)
                has_time_component = any(
                    (hasattr(dt, 'hour') and dt.hour != 0) or
                    (hasattr(dt, 'minute') and dt.minute != 0) or
                    (hasattr(dt, 'second') and dt.second != 0)
                    for dt in sample_times if pd.notna(dt)
                )
                if has_time_component:
                    logger.warning(f"🔧 FORMAT DETECTION FIX: File detected as 'daily' but has time components")
                    logger.warning(f"🔧 CORRECTING: Changing format from 'daily' to 'timestamp' for proper processing")
                    format_type = 'timestamp'

        if format_type == 'unknown':
            logger.error("Could not identify data format")
            raise ValueError("Unable to identify the data format. Please ensure the file contains recognizable timestamp and demand columns.")

    except Exception as e:
        logger.error(f"Format identification failed: {str(e)}")
        raise ValueError(f"Failed to identify data format: {str(e)}")

    # Detect date format
    try:
        date_format = file_processor.detect_date_format(data)
        logger.info(f"Detected date format: {date_format}")

        if date_format == 'unknown':
            logger.warning("Could not detect date format, using default")

    except Exception as e:
        logger.error(f"Date format detection failed: {str(e)}")
        date_format = 'unknown'  # Continue with unknown format

    # Explicitly check for demand units
    try:
        detected_unit = check_demand_units(data, format_type)
        if detected_unit != 'unknown':
            logger.info(f"Detected demand unit: {detected_unit}")
    except Exception as e:
        logger.warning(f"Unit detection failed: {str(e)}")
        detected_unit = 'unknown'

    # Get the appropriate parser with the detected date format
    try:
        parser = get_parser(format_type, detected_format=date_format)
        logger.info(f"Selected parser: {parser.__class__.__name__}")
    except Exception as e:
        logger.error(f"Parser selection failed: {str(e)}")
        raise ValueError(f"Failed to select appropriate parser for format '{format_type}': {str(e)}")

    # Parse the data
    try:
        parsed_data, unit = parser.parse(data)
        logger.info(f"Parser returned {len(parsed_data) if parsed_data else 0} data points with unit: {unit}")
    except Exception as e:
        logger.error(f"Parser failed with error: {str(e)}")
        raise ValueError(f"Failed to parse data with {format_type} parser: {str(e)}")

    if not parsed_data:
        logger.error(f"Parser returned empty data for format: {format_type}")
        raise ValueError(f"No data could be parsed from the input file. The file may be empty, corrupted, or in an unsupported format.")

    # Use the explicitly detected unit if the parser couldn't determine it
    if unit == 'unknown' and detected_unit != 'unknown':
        unit = detected_unit
        logger.info(f"Using explicitly detected unit: {unit}")

    # Analyze data completeness to determine processing strategy
    try:
        logger.info(f"Starting data processing with {len(parsed_data)} parsed data points")

        # RESET ALL GLOBAL PRESERVATION STATE to ensure clean processing
        DataProcessor.preserve_original_precision = False
        logger.debug("🔄 RESET: All global preservation flags cleared")

        # Analyze the data to determine the best processing approach
        analysis = analyze_data_completeness(parsed_data, file_format)

        logger.info(f"📊 DATA ANALYSIS RESULTS:")
        logger.info(f"   Action: {analysis['action'].upper()}")
        logger.info(f"   Reason: {analysis['reason']}")
        logger.info(f"   Data points: {analysis['data_points']}/{analysis['expected_points']}")
        logger.info(f"   Completeness: {analysis['completeness_ratio']:.1%}")
        logger.info(f"   Gaps found: {analysis['gaps_found']}")
        logger.info(f"   Year: {analysis['year']} ({'leap' if analysis['is_leap_year'] else 'regular'})")

        # Override preserve_data based on analysis (unless user explicitly forces it)
        if preserve_data and analysis['action'] == 'complete':
            logger.warning(f"⚠️ OVERRIDE: Data needs completion despite preservation request")
            logger.warning(f"   Reason: {analysis['reason']}")
            logger.warning(f"   Will complete missing data and mark synthetic portions")
            use_smart_completion = True
        elif analysis['action'] == 'preserve':
            logger.info(f"🔒 SMART PRESERVATION: Data is complete, preserving original values")
            use_smart_completion = False
        else:
            logger.info(f"🔄 SMART COMPLETION: Data needs completion")
            use_smart_completion = True

        if not use_smart_completion:
            # Data is complete - use preservation mode
            logger.warning("🔒 ENABLING DATA PRESERVATION MODE - original values will be maintained")

            # Store original methods for restoration
            original_outlier_method = DataProcessor.detect_and_handle_outliers
            original_magnitude_method = DataProcessor._detect_magnitude
            original_cumulative_method = DataProcessor.handle_cumulative_data
            original_aggregate_method = DataProcessor.aggregate_to_hourly

            def preserve_original_data(self, data):
                logger.warning("🚨 OUTLIER DETECTION DISABLED - preserving all original data points")
                return data

            def preserve_original_magnitude(self, values):
                logger.warning("🚨 MAGNITUDE DETECTION DISABLED - no unit scaling applied")
                return 1.0

            def preserve_cumulative_data(self, data):
                logger.warning("🚨 CUMULATIVE DATA CONVERSION DISABLED - preserving original values")
                return data

            def preserve_aggregation(self, data):
                logger.warning("🚨 DATA AGGREGATION DISABLED - preserving original time resolution")
                return data

            # Disable ALL data modification methods
            DataProcessor.detect_and_handle_outliers = preserve_original_data
            DataProcessor._detect_magnitude = preserve_original_magnitude
            DataProcessor.handle_cumulative_data = preserve_cumulative_data
            DataProcessor.aggregate_to_hourly = preserve_aggregation

            # DISABLE DATA GENERATION - NO SYNTHETIC DATA!
            def preserve_no_generation(self, data, year):
                logger.warning("🚨 DATA GENERATION DISABLED - no synthetic data will be created")
                return data  # Return original data only

            DataProcessor.generate_full_year_data = preserve_no_generation

            # DISABLE ALL DATA EXPANSION - PRESERVE ORIGINAL DATA ONLY!
            def preserve_no_expansion(self, data, unit, expand_to='none'):
                logger.warning("🚨 DATA EXPANSION DISABLED - preserving original data only")

                # Convert to list of dictionaries with separate Date and Time fields
                result = []
                for ts, demand in data:
                    # Format date as "MM/DD/YYYY" to avoid year conversion issues
                    date_str = ts.strftime('%m/%d/%Y')
                    # Format time as "HH:MM"
                    time_str = ts.strftime('%H:%M')

                    # Preserve original precision - NO ROUNDING!
                    result.append({
                        "Date": date_str,
                        "Time": time_str,
                        "Demand": demand,  # Original value, no modifications
                        "IsGenerated": False  # All data is original
                    })

                logger.warning(f"🚨 PRESERVED {len(result)} ORIGINAL DATA POINTS - NO MODIFICATIONS")
                return result

            DataProcessor.process_data = preserve_no_expansion

            # Set precision preservation flag GLOBALLY for all components
            DataProcessor.preserve_original_precision = True
            data_processor.preserve_original_precision = True

            # Also set it in output generator if it exists
            try:
                from output_generator import OutputGenerator
                OutputGenerator.preserve_original_precision = True
            except:
                pass

            logger.warning("🚨 PRECISION PRESERVATION ENABLED - original decimal precision will be maintained")
            logger.warning("🚨 DATA GENERATION DISABLED - no synthetic data will be created")
            logger.warning("🚨 DATA EXPANSION DISABLED - preserving original data only")
            logger.warning("🚨 ALL DATA MODIFICATION FUNCTIONS DISABLED")

            try:
                processed_data = data_processor.process_data(parsed_data, unit)
                logger.info(f"Data processing completed successfully with preservation mode, generated {len(processed_data)} final data points")
            finally:
                # Restore ALL original methods to ensure no side effects
                DataProcessor.detect_and_handle_outliers = original_outlier_method
                DataProcessor._detect_magnitude = original_magnitude_method
                DataProcessor.handle_cumulative_data = original_cumulative_method
                DataProcessor.aggregate_to_hourly = original_aggregate_method
                DataProcessor.preserve_original_precision = False
                logger.debug("Restored all original data processing methods")
        else:
            # Smart completion mode - complete missing data intelligently
            logger.info("🔄 USING SMART COMPLETION MODE - will complete missing data")

            # ENSURE ALL PRESERVATION FLAGS ARE DISABLED for completion mode
            DataProcessor.preserve_original_precision = False
            if hasattr(data_processor, 'preserve_original_precision'):
                data_processor.preserve_original_precision = False

            logger.warning("🔄 COMPLETION MODE: All preservation flags disabled, will complete missing data")

            # Use regular processing but with smart completion
            processed_data = data_processor.process_data(parsed_data, unit, expand_to='year')

            # Mark which data points are original vs generated
            if processed_data:
                original_timestamps = set()
                for ts, _ in parsed_data:
                    original_timestamps.add(ts)

                # Mark data points as original or generated
                for item in processed_data:
                    # Parse the timestamp from the item
                    try:
                        from datetime import datetime
                        item_timestamp = datetime.strptime(f"{item['Date']} {item['Time']}", "%m/%d/%Y %H:%M")
                        item['IsGenerated'] = item_timestamp not in original_timestamps
                    except:
                        item['IsGenerated'] = True  # Assume generated if can't parse

                original_count = sum(1 for item in processed_data if not item.get('IsGenerated', True))
                generated_count = len(processed_data) - original_count

                logger.info(f"Smart completion results: {original_count} original + {generated_count} generated = {len(processed_data)} total points")

            logger.info(f"Data processing completed successfully with smart completion, generated {len(processed_data)} final data points")

    except TimeoutError as e:
        logger.error(f"Data processing timed out: {str(e)}")
        raise ValueError(f"Data processing took too long and was terminated. The file may be too large or complex.")
    except Exception as e:
        logger.error(f"Data processing failed with error: {str(e)}")
        raise ValueError(f"Failed to process the parsed data: {str(e)}")

    return processed_data

def main():
    """
    Main function
    """
    parser = argparse.ArgumentParser(description='Process power demand data files (CSV, JSON, TXT, Excel)')
    parser.add_argument('--input_dir', type=str, default='.', help='Input directory containing data files (local)')
    parser.add_argument('--output_file', type=str, default='output.csv', help='Output file (CSV by default)')
    parser.add_argument('--file', type=str, help='Process a specific file (local or S3)')
    parser.add_argument('--target_range', type=float, default=100.0, help='Target range parameter (not used for scaling anymore)')
    parser.add_argument('--s3', action='store_true', help='Use S3 for file input')
    parser.add_argument('--upload_to_s3', action='store_true', help='Upload output to S3')
    parser.add_argument('--bucket', type=str, help='Override S3 bucket name from .env')
    parser.add_argument('--list_files', action='store_true', help='List files in the S3 bucket')
    parser.add_argument('--prefix', type=str, default='', help='Prefix for listing S3 files')
    parser.add_argument('--overwrite', action='store_true', help='Overwrite output file instead of merging data')
    parser.add_argument('--output_format', type=str, default='csv', choices=['csv', 'json'], help='Output format (csv or json)')
    parser.add_argument('--preserve_data', action='store_true', help='Force data preservation mode (maintains original values exactly)')
    parser.add_argument('--allow_modifications', action='store_true', help='Allow data modifications (outlier detection, scaling, etc.)')
    parser.add_argument('--smart_mode', action='store_true', default=True, help='Use smart processing (preserve complete data, complete incomplete data) - DEFAULT: True')
    parser.add_argument('--force_complete', action='store_true', help='Force completion even for complete data')
    # Note: expand_to parameter is no longer used as it's always set to 'year' in the backend

    args = parser.parse_args()

    # Initialize components
    output_generator = OutputGenerator(args.input_dir, overwrite=args.overwrite)
    llm_handler = LLMHandler(LLM_CONFIG)

    # Process files
    all_data = []

    # Initialize S3 handler if needed
    if args.s3 or args.list_files:
        s3_handler = S3FileHandler(bucket_name=args.bucket)

    # List files in S3 bucket if requested
    if args.list_files:
        s3_files = s3_handler.list_files(prefix=args.prefix, verbose=True)
        logger.info(f"Found {len(s3_files)} files in S3 bucket {s3_handler.bucket_name}")
        return  # Exit after listing files

    if args.s3 and args.file:
        # Process a specific file from S3
        processed_data = process_s3_file(args.file, llm_handler, bucket_name=args.bucket)
        all_data.append(processed_data)
    elif args.s3:
        # Process all files in S3 bucket
        s3_files = s3_handler.list_files(prefix=args.prefix)
        logger.info(f"Found {len(s3_files)} files in S3 bucket {s3_handler.bucket_name}")

        for s3_key in s3_files:
            # Skip the output files if they exist
            if s3_key.endswith('.json'):
                continue

            # Determine preservation mode: default to True unless explicitly overridden
            use_preservation = args.preserve_data and not args.allow_modifications
            if args.allow_modifications:
                logger.warning("🚨 DATA MODIFICATIONS ENABLED - original values may be changed")
            else:
                logger.info("🔒 DATA PRESERVATION ENABLED - original values will be maintained")

            processed_data = process_s3_file(s3_key, llm_handler, bucket_name=args.bucket, preserve_data=use_preservation)
            all_data.append(processed_data)
    elif args.file:
        # Process a specific local file
        file_path = os.path.join(args.input_dir, args.file)
        # Determine processing mode based on user preferences
        if args.preserve_data:
            logger.info("🔒 FORCED PRESERVATION MODE - will preserve data regardless of completeness")
            use_preservation = True
        elif args.force_complete:
            logger.info("🔄 FORCED COMPLETION MODE - will complete data regardless of completeness")
            use_preservation = False
        elif args.smart_mode:
            logger.info("🧠 SMART MODE - will analyze data and choose best approach")
            use_preservation = None  # Will be determined by analysis
        else:
            logger.info("� REGULAR MODE - will use standard processing")
            use_preservation = False

        processed_data = process_file(file_path, llm_handler, preserve_data=use_preservation)
        all_data.append(processed_data)
    else:
        # Process all files in the local directory
        file_processor = FileProcessor(args.input_dir)
        files = file_processor.get_files()

        for file_path in files:
            # Skip the output files if they exist
            if os.path.basename(file_path) == args.output_file or os.path.basename(file_path) == 'test_output.json':
                continue

            # Determine preservation mode: FORCE preservation unless explicitly disabled
            use_preservation = (args.force_preserve or args.preserve_data) and not args.allow_modifications
            if args.allow_modifications:
                logger.warning("🚨 DATA MODIFICATIONS ENABLED - original values may be changed")
                use_preservation = False
            else:
                logger.warning("🔒 FORCED DATA PRESERVATION ENABLED - original values will be maintained")
                use_preservation = True

            processed_data = process_file(file_path, llm_handler, preserve_data=use_preservation)
            all_data.append(processed_data)

    # Merge all data
    merged_data = output_generator.merge_data(all_data)

    # Format the output
    formatted_data = output_generator.format_output(merged_data)

    # Generate the output file
    if args.upload_to_s3:
        # Upload to S3
        s3_handler = S3FileHandler()

        # Use the appropriate upload method based on the output format
        if args.output_format.lower() == 'csv':
            success = s3_handler.upload_csv(formatted_data, args.output_file)
            if success:
                logger.info(f"Successfully uploaded CSV output to S3: {args.output_file}")
            else:
                logger.error("Failed to upload CSV output to S3")
        else:
            success = s3_handler.upload_json(formatted_data, args.output_file)
            if success:
                logger.info(f"Successfully uploaded JSON output to S3: {args.output_file}")
            else:
                logger.error("Failed to upload JSON output to S3")
    else:
        # Save locally
        if args.output_format.lower() == 'csv':
            # Convert to CSV with Timestamp and Demand columns
            rows = []
            for item in formatted_data:
                timestamp = f"{item['Date']} {item['Time']}"
                demand = item['Demand']
                rows.append({"Timestamp": timestamp, "Demand": demand})

            df = pd.DataFrame(rows)
            output_path = os.path.join(args.input_dir, args.output_file)
            df.to_csv(output_path, index=False)
            logger.info(f"Successfully generated CSV output file: {output_path}")
        else:
            # Save as JSON
            output_path = output_generator.generate_json(formatted_data, args.output_file)
            if output_path:
                logger.info(f"Successfully generated JSON output file: {output_path}")
            else:
                logger.error("Failed to generate JSON output file")

if __name__ == "__main__":
    main()
