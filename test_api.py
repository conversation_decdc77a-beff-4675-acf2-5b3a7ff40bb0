import requests
import json
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_process_file_api(base_url, input_file, output_path, api_key=None):
    """
    Test the process-file API endpoint

    Args:
        base_url: Base URL of the API
        input_file: S3 path to the input file
        output_path: S3 path where to save the output file
        api_key: API key for authentication (optional, will use from .env if not provided)
    """
    # Get API key from environment if not provided
    if not api_key:
        api_key = os.getenv("API_KEY", "your-default-api-key")

    # Construct the request URL
    url = f"{base_url}/process-file"

    # Prepare the request payload
    payload = {
        "input_file": input_file,
        "output_path": output_path
    }

    # Prepare headers with API key
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": api_key
    }

    # Send the request
    print(f"Sending request to {url} with payload: {json.dumps(payload, indent=2)}")
    print(f"Using API key: {api_key}")
    response = requests.post(url, json=payload, headers=headers)

    # Print the response
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response body: {json.dumps(response.json(), indent=2)}")
    else:
        print(f"Response body: {response.text}")

def test_process_metadata_api(base_url, input_file, output_path, country_code, industry_type, sub_industry_type, api_key=None):
    """
    Test the process-metadata API endpoint

    Args:
        base_url: Base URL of the API
        input_file: S3 path to the input file
        output_path: S3 path where to save the output file
        country_code: Country code (e.g., 'IN' for India)
        industry_type: Industry type (e.g., 'Commercial')
        sub_industry_type: Sub-industry type (e.g., 'Data-center')
        api_key: API key for authentication (optional, will use from .env if not provided)
    """
    # Get API key from environment if not provided
    if not api_key:
        api_key = os.getenv("API_KEY", "your-default-api-key")

    # Construct the request URL
    url = f"{base_url}/process-metadata"

    # Prepare the request payload
    payload = {
        "input_file": input_file,
        "output_path": output_path,
        "country_code": country_code,
        "industry_type": industry_type,
        "sub_industry_type": sub_industry_type
    }

    # Prepare headers with API key
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": api_key
    }

    # Send the request
    print(f"Sending request to {url} with payload: {json.dumps(payload, indent=2)}")
    print(f"Using API key: {api_key}")
    response = requests.post(url, json=payload, headers=headers)

    # Print the response
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response body: {json.dumps(response.json(), indent=2)}")
    else:
        print(f"Response body: {response.text}")

def test_api(base_url, input_file, output_path, api_key=None):
    """
    Test the process-file API endpoint (legacy function for backward compatibility)

    Args:
        base_url: Base URL of the API
        input_file: S3 path to the input file
        output_path: S3 path where to save the output file
        api_key: API key for authentication (optional, will use from .env if not provided)
    """
    return test_process_file_api(base_url, input_file, output_path, api_key)

if __name__ == "__main__":
    # Check if enough arguments are provided
    if len(sys.argv) < 3:
        print("Usage:")
        print("1. Test process-file endpoint:")
        print("   python test_api.py file <base_url> <input_file> <output_path> [api_key]")
        print("   Example: python test_api.py file http://localhost:8000 path/to/input.csv path/to/output/")
        print("")
        print("2. Test process-metadata endpoint:")
        print("   python test_api.py metadata <base_url> <input_file> <output_path> <country_code> <industry_type> <sub_industry_type> [api_key]")
        print("   Example: python test_api.py metadata http://localhost:8000 path/to/input.csv path/to/output/ IN Commercial Data-center")
        print("")
        print("If api_key is not provided, it will be read from the .env file")
        sys.exit(1)

    # Get the test type
    test_type = sys.argv[1]

    if test_type == "file":
        # Check if the required arguments for process-file are provided
        if len(sys.argv) < 5:
            print("Usage for process-file test:")
            print("python test_api.py file <base_url> <input_file> <output_path> [api_key]")
            sys.exit(1)

        # Get the required arguments
        base_url = sys.argv[2]
        input_file = sys.argv[3]
        output_path = sys.argv[4]

        # Get optional argument
        api_key = None
        if len(sys.argv) > 5:
            api_key = sys.argv[5]

        # Test the process-file API
        test_process_file_api(base_url, input_file, output_path, api_key)

    elif test_type == "metadata":
        # Check if the required arguments for process-metadata are provided
        if len(sys.argv) < 8:
            print("Usage for process-metadata test:")
            print("python test_api.py metadata <base_url> <input_file> <output_path> <country_code> <industry_type> <sub_industry_type> [api_key]")
            sys.exit(1)

        # Get the required arguments
        base_url = sys.argv[2]
        input_file = sys.argv[3]
        output_path = sys.argv[4]
        country_code = sys.argv[5]
        industry_type = sys.argv[6]
        sub_industry_type = sys.argv[7]

        # Get optional argument
        api_key = None
        if len(sys.argv) > 8:
            api_key = sys.argv[8]

        # Test the process-metadata API
        test_process_metadata_api(base_url, input_file, output_path, country_code, industry_type, sub_industry_type, api_key)

    else:
        # Legacy mode for backward compatibility
        if len(sys.argv) < 4:
            print("Usage: python test_api.py <base_url> <input_file> <output_path> [api_key]")
            sys.exit(1)

        # Get the required arguments
        base_url = sys.argv[1]
        input_file = sys.argv[2]
        output_path = sys.argv[3]

        # Get optional argument
        api_key = None
        if len(sys.argv) > 4:
            api_key = sys.argv[4]

        # Test the API using the legacy function
        test_api(base_url, input_file, output_path, api_key)
