import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "app"))

# Import the necessary modules
from services import get_timezone_from_country, get_alpha2_code

def test_country_codes():
    """
    Test the country code conversion and timezone lookup
    """
    # Test cases with different formats
    test_cases = [
        # 2-letter codes
        "US",
        "IN",
        "GB",
        # 3-letter codes
        "USA",
        "IND",
        "GBR",
        # Country names
        "United States",
        "India",
        "United Kingdom",
        # Mixed case
        "usa",
        "iNd",
        "United kingdom",
        # Invalid codes
        "XX",
        "XYZ",
        "Nonexistent Country"
    ]
    
    print("Testing country code conversion and timezone lookup:")
    print("=" * 60)
    print(f"{'Country Identifier':<25} {'2-Letter Code':<15} {'Timezone':<15}")
    print("-" * 60)
    
    for identifier in test_cases:
        alpha2 = get_alpha2_code(identifier)
        timezone = get_timezone_from_country(identifier)
        print(f"{identifier:<25} {alpha2 if alpha2 else 'None':<15} {timezone if timezone else 'None':<15}")
    
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_country_codes()
