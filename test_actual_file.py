#!/usr/bin/env python3
"""
Test script to process the actual "USA AI Datacenter Demand (1).xlsx" file
"""

import os
import sys
import time
import pandas as pd
from datetime import datetime

# Add the current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import processing modules
from main_s3 import process_data
from file_processor import FileProcessor
from data_processor import DataProcessor
from llm_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_actual_file_processing():
    """Test processing the actual Excel file"""
    print("🔧 Testing Actual File Processing")
    print("=" * 50)
    
    file_path = "USA AI Datacenter Demand (1).xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Step 1: Read the Excel file
        print("📖 Step 1: Reading Excel file...")
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"   ✓ File read successfully: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"   ✓ Columns: {list(df.columns)}")
        
        # Check for December dates
        december_count = len(df[df['Date'].dt.month == 12])
        print(f"   ✓ December data points: {december_count}")
        
        # Step 2: Process the data
        print("\n⚙️ Step 2: Processing data through pipeline...")
        start_time = time.time()
        
        # Initialize components
        file_processor = FileProcessor()
        data_processor = DataProcessor()
        
        # Process through our improved pipeline
        processed_data = process_data(df, 'excel', llm_handler=None)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"   ✓ Processing completed in {processing_time:.2f} seconds")
        print(f"   ✓ Output data points: {len(processed_data)}")
        
        # Step 3: Validate the output
        print("\n✅ Step 3: Validating output...")
        
        if not processed_data:
            print("   ❌ No output data generated")
            return False
        
        # Check if we have the expected structure
        sample_item = processed_data[0]
        expected_keys = ['Date', 'Time', 'Demand']
        if not all(key in sample_item for key in expected_keys):
            print(f"   ❌ Missing expected keys. Found: {list(sample_item.keys())}")
            return False
        
        print(f"   ✓ Output structure correct: {list(sample_item.keys())}")
        
        # Check for December data in output
        december_output = [item for item in processed_data if '12/' in item['Date']]
        print(f"   ✓ December data in output: {len(december_output)} points")
        
        # Show sample output
        print(f"\n📊 Sample output (first 5 points):")
        for i, item in enumerate(processed_data[:5]):
            print(f"   {i+1}. {item['Date']} {item['Time']} -> {item['Demand']}")
        
        print(f"\n📊 Sample December output (first 3 December points):")
        for i, item in enumerate(december_output[:3]):
            print(f"   {i+1}. {item['Date']} {item['Time']} -> {item['Demand']}")
        
        # Performance validation
        if processing_time > 30:  # More than 30 seconds
            print(f"   ⚠️ Processing time ({processing_time:.2f}s) is longer than expected")
        else:
            print(f"   ✓ Processing time ({processing_time:.2f}s) is acceptable")
        
        print(f"\n🎉 SUCCESS: File processed successfully!")
        print(f"   • Input: {df.shape[0]} Excel rows with December dates")
        print(f"   • Output: {len(processed_data)} processed data points")
        print(f"   • Processing time: {processing_time:.2f} seconds")
        print(f"   • December handling: ✓ Working correctly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        print(f"Traceback:\n{traceback.format_exc()}")
        return False

def test_format_detection():
    """Test format detection specifically"""
    print("\n🔍 Testing Format Detection")
    print("=" * 30)
    
    try:
        # Read the file
        df = pd.read_excel("USA AI Datacenter Demand (1).xlsx", sheet_name=0)
        
        # Test format detection
        file_processor = FileProcessor()
        detected_format = file_processor.identify_format(df)
        print(f"   ✓ Detected format: {detected_format}")
        
        # Test date format detection
        date_format = file_processor.detect_date_format(df)
        print(f"   ✓ Detected date format: {date_format}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Format detection failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Improved Demand Data Pipeline with Actual File")
    print("=" * 60)
    
    success = True
    
    # Test format detection
    if not test_format_detection():
        success = False
    
    # Test actual file processing
    if not test_actual_file_processing():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL TESTS PASSED! The improvements are working correctly.")
        print("\nKey validations:")
        print("  ✓ Excel file reading with datetime objects")
        print("  ✓ December date handling")
        print("  ✓ Performance optimization")
        print("  ✓ Data structure validation")
        print("  ✓ Error handling")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
