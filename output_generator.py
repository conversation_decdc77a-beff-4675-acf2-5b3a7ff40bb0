import json
import os
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OutputGenerator:
    """
    Generates the final JSON output
    """

    def __init__(self, output_dir: str = None, overwrite: bool = False):
        """
        Initialize the OutputGenerator

        Args:
            output_dir: Directory to save output files
            overwrite: Whether to overwrite existing output files
        """
        self.output_dir = output_dir or os.getcwd()
        self.overwrite = overwrite

    def generate_json(self, data: List[Dict[str, Any]], output_file: str = 'output.json') -> str:
        """
        Generate a JSON file with the processed data

        Args:
            data: List of dictionaries with Date, Time, and Demand fields
            output_file: Name of the output file

        Returns:
            Path to the generated file
        """
        output_path = os.path.join(self.output_dir, output_file)

        try:
            with open(output_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Successfully generated JSON output: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error generating JSON output: {str(e)}")
            return None

    def merge_data(self, data_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Merge multiple data lists

        Args:
            data_list: List of data lists (each containing dictionaries with Date, Time, and Demand fields)

        Returns:
            Merged data list
        """
        if not data_list:
            return []

        # If overwrite is True, just return the last list
        if self.overwrite and len(data_list) > 0:
            logger.info("Overwrite mode: using only the most recent data")
            return data_list[-1].copy()

        # Create a dictionary to store merged data
        # Key will be a tuple of (Date, Time)
        merged_dict = {}

        # Process all data lists
        for data_list_item in data_list:
            for item in data_list_item:
                key = (item['Date'], item['Time'])
                if key in merged_dict:
                    # Average the demand values if the key already exists
                    merged_dict[key] = {
                        'Date': item['Date'],
                        'Time': item['Time'],
                        'Demand': (merged_dict[key]['Demand'] + item['Demand']) / 2
                    }
                else:
                    # Add the item if it doesn't exist
                    merged_dict[key] = item.copy()

        # Convert back to list
        merged_data = list(merged_dict.values())

        return merged_data

    def format_output(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format the output data according to requirements

        Args:
            data: List of dictionaries with Date, Time, and Demand fields

        Returns:
            Formatted data list
        """
        # Sort by Date and Time
        sorted_data = sorted(data, key=lambda x: (x['Date'], x['Time']))

        # Round Demand values to 2 decimal places
        for item in sorted_data:
            item['Demand'] = round(item['Demand'], 2)

        return sorted_data
