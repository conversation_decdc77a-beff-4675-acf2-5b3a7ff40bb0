#!/usr/bin/env python3
"""
Test script to verify that data values are truly preserved and not modified
"""

import os
import sys
import pandas as pd
import numpy as np
from unittest.mock import patch

# Add the app directory to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "app"))

from services import process_s3_file_preserve_original
from s3_file_handler import S3FileHandler

def test_data_preservation():
    """Test that original data values are preserved exactly"""
    print("🔒 Testing Data Preservation - Values Should NOT Change")
    print("=" * 60)
    
    # Read the actual Excel file
    file_path = "USA AI Datacenter Demand (1).xlsx"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Read the Excel file to get the original data
    df = pd.read_excel(file_path, sheet_name=0)
    print(f"📖 Loaded original file: {df.shape[0]} rows, {df.shape[1]} columns")
    
    # Get original values for comparison
    original_values = df.iloc[:, 1].values  # Second column (demand values)
    print(f"📊 Original data sample (first 10 values):")
    for i in range(min(10, len(original_values))):
        print(f"   {i+1}. {original_values[i]}")
    
    print(f"\n📊 Original data statistics:")
    print(f"   Min: {np.min(original_values):.6f}")
    print(f"   Max: {np.max(original_values):.6f}")
    print(f"   Mean: {np.mean(original_values):.6f}")
    print(f"   Sum: {np.sum(original_values):.6f}")
    
    # Mock S3 operations to use local file
    def mock_read_file(file_name):
        """Mock S3 file reading to return our local Excel data"""
        print(f"   🔄 Mock S3: Reading {file_name}")
        return df, 'excel'
    
    try:
        # Test the data preservation function
        with patch.object(S3FileHandler, 'read_file', side_effect=mock_read_file):
            print(f"\n⚙️ Processing with COMPLETE data preservation...")
            
            result = process_s3_file_preserve_original(
                file_name="USA AI Datacenter Demand (1).xlsx",
                llm_handler=None,
                bucket_name=None
            )
            
            if not result:
                print("❌ No result returned from processing")
                return False
            
            print(f"✅ Processing completed, got {len(result)} data points")
            
            # Extract processed values
            processed_values = [float(item['Demand']) for item in result]
            
            print(f"\n📊 Processed data sample (first 10 values):")
            for i in range(min(10, len(processed_values))):
                print(f"   {i+1}. {processed_values[i]}")
            
            print(f"\n📊 Processed data statistics:")
            print(f"   Min: {np.min(processed_values):.6f}")
            print(f"   Max: {np.max(processed_values):.6f}")
            print(f"   Mean: {np.mean(processed_values):.6f}")
            print(f"   Sum: {np.sum(processed_values):.6f}")
            
            # Compare values
            print(f"\n🔍 COMPARISON ANALYSIS:")
            
            # Check if we have the same number of data points
            if len(original_values) != len(processed_values):
                print(f"❌ MISMATCH: Different number of data points!")
                print(f"   Original: {len(original_values)} points")
                print(f"   Processed: {len(processed_values)} points")
                return False
            
            # Check for exact value matches
            exact_matches = 0
            value_differences = []
            
            for i in range(len(original_values)):
                orig_val = float(original_values[i])
                proc_val = float(processed_values[i])
                
                if abs(orig_val - proc_val) < 1e-10:  # Essentially equal
                    exact_matches += 1
                else:
                    diff = abs(orig_val - proc_val)
                    value_differences.append((i, orig_val, proc_val, diff))
            
            match_percentage = (exact_matches / len(original_values)) * 100
            
            print(f"   Exact matches: {exact_matches}/{len(original_values)} ({match_percentage:.2f}%)")
            
            if match_percentage == 100.0:
                print(f"   ✅ PERFECT: All values preserved exactly!")
                return True
            else:
                print(f"   ❌ PROBLEM: {len(value_differences)} values were modified!")
                
                # Show first few differences
                print(f"\n🚨 First 10 value differences:")
                for i, (idx, orig, proc, diff) in enumerate(value_differences[:10]):
                    print(f"   {i+1}. Index {idx}: {orig} → {proc} (diff: {diff})")
                
                # Analyze the pattern of differences
                if value_differences:
                    diffs = [diff for _, _, _, diff in value_differences]
                    print(f"\n📈 Difference statistics:")
                    print(f"   Min difference: {np.min(diffs):.10f}")
                    print(f"   Max difference: {np.max(diffs):.10f}")
                    print(f"   Mean difference: {np.mean(diffs):.10f}")
                
                return False
                
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_specific_scenarios():
    """Test specific scenarios that might cause data modification"""
    print(f"\n🧪 Testing Specific Data Modification Scenarios")
    print("=" * 50)
    
    # Test 1: Check if cumulative detection is triggered
    print(f"\n1. Testing cumulative data detection...")
    
    # Create test data that looks cumulative but isn't
    test_data = [
        (pd.Timestamp('2023-01-01 00:00:00'), 0.82),
        (pd.Timestamp('2023-01-01 01:00:00'), 0.81),  # Decreasing - not cumulative
        (pd.Timestamp('2023-01-01 02:00:00'), 0.80),
        (pd.Timestamp('2023-01-01 03:00:00'), 0.85),  # Increasing again
    ]
    
    from data_processor import DataProcessor
    processor = DataProcessor()
    
    # Test original method (should detect as non-cumulative)
    result = processor.handle_cumulative_data(test_data)
    
    if len(result) == len(test_data):
        values_changed = any(abs(orig[1] - res[1]) > 1e-10 for orig, res in zip(test_data, result))
        if values_changed:
            print(f"   ❌ Values were modified by cumulative detection!")
            for orig, res in zip(test_data, result):
                print(f"      {orig[1]} → {res[1]}")
        else:
            print(f"   ✅ Values preserved correctly")
    else:
        print(f"   ❌ Number of data points changed: {len(test_data)} → {len(result)}")

def main():
    """Run all data preservation tests"""
    print("🔒 COMPREHENSIVE DATA PRESERVATION TESTING")
    print("=" * 70)
    
    success = True
    
    # Test main data preservation
    if not test_data_preservation():
        success = False
    
    # Test specific scenarios
    test_specific_scenarios()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ DATA PRESERVATION VERIFIED!")
        print("\nKey findings:")
        print("  ✓ All original values preserved exactly")
        print("  ✓ No cumulative data conversion applied")
        print("  ✓ No outlier detection modifications")
        print("  ✓ No aggregation or interpolation")
        print("  ✓ Complete data integrity maintained")
    else:
        print("❌ DATA PRESERVATION FAILED!")
        print("\nIssues found:")
        print("  • Original values are being modified")
        print("  • Data processing steps are changing the data")
        print("  • The 'preserve original' mode is not working correctly")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
