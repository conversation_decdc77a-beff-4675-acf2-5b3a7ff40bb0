import requests
import json
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LLMHandler:
    """
    Handles interactions with the LLM for complex parsing tasks
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the LLMHandler
        
        Args:
            config: LLM configuration
        """
        self.api_base = config.get('api_base', 'http://localhost:11435/v1')
        self.api_key = config.get('api_key', 'dummy-key')
        self.model_name = config.get('model_name', 'gemma3:12b')
        self.temperature = config.get('temperature', 0)
    
    def extract_timestamp_format(self, sample_data: str) -> str:
        """
        Use LLM to extract timestamp format from sample data
        
        Args:
            sample_data: Sample data string
            
        Returns:
            Timestamp format string
        """
        prompt = f"""
        Analyze the following data sample and identify the timestamp format:
        
        {sample_data}
        
        Return only the timestamp format as a Python datetime format string (e.g., '%Y-%m-%d %H:%M:%S').
        """
        
        response = self._call_llm(prompt)
        
        # Extract format string from response
        format_string = response.strip().replace("'", "").replace('"', '')
        
        return format_string
    
    def identify_data_structure(self, sample_data: str) -> Dict[str, Any]:
        """
        Use LLM to identify the structure of the data
        
        Args:
            sample_data: Sample data string
            
        Returns:
            Dictionary with data structure information
        """
        prompt = f"""
        Analyze the following data sample and identify its structure:
        
        {sample_data}
        
        Return a JSON object with the following information:
        1. Data format (CSV, JSON, etc.)
        2. Timestamp column name or key
        3. Timestamp format
        4. Demand column name or key
        5. Demand unit (MW, kW, etc.)
        6. Whether the data is cumulative or not
        7. Whether the data is sub-hourly or hourly
        8. Whether the data has separate Date and Time columns
        """
        
        response = self._call_llm(prompt)
        
        try:
            # Parse JSON response
            structure = json.loads(response)
            return structure
        except json.JSONDecodeError:
            logger.error(f"Failed to parse LLM response as JSON: {response}")
            return {}
    
    def _call_llm(self, prompt: str) -> str:
        """
        Call the LLM API
        
        Args:
            prompt: Prompt string
            
        Returns:
            LLM response
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.temperature
            }
            
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                logger.error(f"LLM API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error calling LLM API: {str(e)}")
            return ""
