#!/usr/bin/env python3
"""
Test complete elimination of data modification
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_complete_elimination():
    """Test that ALL data modification is eliminated"""
    print("🔥 TESTING COMPLETE ELIMINATION OF DATA MODIFICATION")
    print("=" * 70)
    
    input_file = "manyata 13 1.xlsx"
    output_file = "test_elimination.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test with the new complete elimination
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--force_preserve",  # Force preservation mode
        "--output_file", output_file,
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Processing completed: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(6, len(lines))):
                print(f"   {lines[i].strip()}")
            
            # Check for the key indicators
            success_indicators = []
            
            # 1. Check year format
            if len(lines) > 1:
                first_line = lines[1].strip()
                if "2023" in first_line:
                    print(f"   ✅ YEAR: Shows 2023 (4-digit year format)")
                    success_indicators.append("year_correct")
                elif "/2023 " in first_line:
                    print(f"   ✅ YEAR: Shows 2023 (correct year)")
                    success_indicators.append("year_correct")
                elif "2025" in first_line or "/25 " in first_line:
                    print(f"   ❌ YEAR: Still shows 2025/25 (wrong)")
                else:
                    print(f"   ⚠️ YEAR: Format unclear: {first_line}")
            
            # 2. Check if we have original data count (not expanded)
            data_count = len(lines) - 1
            if data_count < 1000:  # Original data, not expanded
                print(f"   ✅ DATA COUNT: {data_count} points (original data, not expanded)")
                success_indicators.append("original_count")
            elif data_count > 8000:  # Expanded data
                print(f"   ❌ DATA COUNT: {data_count} points (data was expanded)")
            else:
                print(f"   ⚠️ DATA COUNT: {data_count} points (unclear)")
            
            # 3. Check for preservation messages in logs
            if "DATA GENERATION DISABLED" in result.stdout or "DATA EXPANSION DISABLED" in result.stdout:
                print(f"   ✅ LOGS: Data generation/expansion disabled")
                success_indicators.append("generation_disabled")
            else:
                print(f"   ❌ LOGS: No clear indication of data generation being disabled")
            
            # 4. Check if values look like original (not synthetic patterns)
            if len(lines) > 5:
                values = []
                for i in range(1, min(6, len(lines))):
                    parts = lines[i].strip().split(',')
                    if len(parts) >= 2:
                        try:
                            values.append(float(parts[1]))
                        except:
                            pass
                
                if values:
                    # Check if values are NOT the synthetic pattern we saw before
                    if not (50 <= values[0] <= 80 and all(50 <= v <= 80 for v in values)):
                        print(f"   ✅ VALUES: Don't match synthetic pattern (likely original)")
                        success_indicators.append("values_original")
                    else:
                        print(f"   ❌ VALUES: Match synthetic pattern (55-80 range)")
            
            # Overall assessment
            print(f"\n🎯 ASSESSMENT:")
            print(f"   Success indicators: {len(success_indicators)}/4")
            print(f"   Indicators found: {success_indicators}")
            
            if len(success_indicators) >= 3:
                print(f"   ✅ ELIMINATION SUCCESSFUL!")
                return True
            else:
                print(f"   ❌ ELIMINATION FAILED - still modifying data")
                return False
        else:
            print(f"❌ Processing failed")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Processing timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def compare_all_outputs():
    """Compare all outputs to see the progression"""
    print(f"\n📊 COMPARING ALL OUTPUTS")
    print("=" * 70)
    
    files_to_compare = [
        ("my_results7.csv", "Previous attempt (WRONG)"),
        ("my_results8.csv", "Recent attempt (STILL WRONG)"),
        ("test_elimination.csv", "Complete elimination (should be CORRECT)")
    ]
    
    for filename, description in files_to_compare:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            print(f"\n📁 {description}:")
            print(f"   File: {filename}")
            print(f"   Lines: {len(lines)}")
            
            if len(lines) > 1:
                first_line = lines[1].strip()
                print(f"   First data: {first_line}")
                
                # Extract year and value
                parts = first_line.split(',')
                if len(parts) >= 2:
                    date_part = parts[0]
                    value_part = parts[1]
                    
                    # Check year
                    if "2023" in date_part:
                        print(f"   Year: ✅ 2023")
                    elif "2025" in date_part or "/25 " in date_part:
                        print(f"   Year: ❌ 2025")
                    else:
                        print(f"   Year: ⚠️ Unknown")
                    
                    # Check value range
                    try:
                        value = float(value_part)
                        if 50 <= value <= 80:
                            print(f"   Value: ❌ Synthetic pattern ({value})")
                        else:
                            print(f"   Value: ✅ Likely original ({value})")
                    except:
                        print(f"   Value: ⚠️ Cannot parse")
                
                # Check data count
                data_count = len(lines) - 1
                if data_count < 1000:
                    print(f"   Count: ✅ Original data ({data_count})")
                elif data_count > 8000:
                    print(f"   Count: ❌ Expanded data ({data_count})")
                else:
                    print(f"   Count: ⚠️ Unclear ({data_count})")
        else:
            print(f"\n⚠️ {description}: File not found ({filename})")

def main():
    """Run the complete elimination test"""
    print("🔥 COMPLETE ELIMINATION OF DATA MODIFICATION")
    print("=" * 70)
    
    success = test_complete_elimination()
    compare_all_outputs()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ COMPLETE ELIMINATION SUCCESSFUL!")
        print("\n🎯 What was eliminated:")
        print("  • Data generation (no synthetic values)")
        print("  • Data expansion (no full year generation)")
        print("  • Outlier detection (no value modifications)")
        print("  • Magnitude scaling (no unit conversions)")
        print("  • Value rounding (preserve original precision)")
        print("  • Pattern application (no artificial patterns)")
        print("\n🚀 Now the API can use the same logic!")
    else:
        print("❌ ELIMINATION FAILED!")
        print("\n🔍 Still happening:")
        print("  • Check if data is still being generated/expanded")
        print("  • Check if values are still being modified")
        print("  • Check if year is still being converted")
        print("  • May need more aggressive elimination")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
