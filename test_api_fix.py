#!/usr/bin/env python3
"""
Test script to verify the API fix produces the same results as main_s3.py
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_vs_local():
    """Test that API and local main_s3.py produce identical results"""
    print("🔍 TESTING API vs LOCAL CONSISTENCY")
    print("=" * 60)
    
    input_file = "USA AI Datacenter Demand (1).xlsx"
    local_output = "test_local_fixed.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    try:
        # Test 1: Run local main_s3.py with preservation (should match your my_results6.csv)
        print(f"🔧 Test 1: Running local main_s3.py with preservation...")
        
        cmd = [
            "python3", "main_s3.py",
            "--file", input_file,
            "--output_file", local_output,
            "--overwrite"
            # Default is preservation mode now
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"   ✅ Local processing completed successfully")
            
            if os.path.exists(local_output):
                # Read and analyze local output
                with open(local_output, 'r') as f:
                    local_lines = f.readlines()
                
                print(f"   📊 Local output analysis:")
                print(f"      Total lines: {len(local_lines)}")
                print(f"      Data points: {len(local_lines) - 1}")
                
                # Show sample data
                print(f"   📋 Sample local data:")
                for i in range(1, min(6, len(local_lines))):
                    print(f"      {i}. {local_lines[i].strip()}")
                
                # Compare with existing my_results6.csv
                if os.path.exists("my_results6.csv"):
                    with open("my_results6.csv", 'r') as f:
                        original_lines = f.readlines()
                    
                    print(f"\n   🔍 Comparison with my_results6.csv:")
                    print(f"      Original lines: {len(original_lines)}")
                    print(f"      New lines: {len(local_lines)}")
                    
                    # Compare first few lines
                    matches = 0
                    for i in range(1, min(6, len(original_lines), len(local_lines))):
                        if original_lines[i].strip() == local_lines[i].strip():
                            matches += 1
                        else:
                            print(f"      ❌ Difference at line {i}:")
                            print(f"         Original: {original_lines[i].strip()}")
                            print(f"         New:      {local_lines[i].strip()}")
                    
                    if matches == 5:
                        print(f"      ✅ Local output matches my_results6.csv")
                    else:
                        print(f"      ❌ Local output differs from my_results6.csv")
                
            else:
                print(f"   ❌ Local output file not created")
                return False
        else:
            print(f"   ❌ Local processing failed: {result.stderr}")
            return False
        
        # Test 2: Test API function directly
        print(f"\n🌐 Test 2: Testing API function directly...")
        
        try:
            sys.path.append('.')
            from app.services import process_s3_file_smart_preservation
            from llm_handler import LLMHandler
            
            # LLM configuration
            LLM_CONFIG = {
                "api_base": "http://*************:11435/v1",
                "api_key": "dummy-key", 
                "model_name": "gemma3:12b",
                "temperature": 0
            }
            
            llm_handler = LLMHandler(LLM_CONFIG)
            
            print(f"   🔄 Processing with API function...")
            api_result = process_s3_file_smart_preservation(input_file, llm_handler, bucket_name=None)
            
            if api_result:
                print(f"   ✅ API processing completed successfully")
                print(f"   📊 API result analysis:")
                print(f"      Data points: {len(api_result)}")
                
                # Show sample data
                print(f"   📋 Sample API data:")
                for i in range(min(5, len(api_result))):
                    item = api_result[i]
                    print(f"      {i+1}. {item}")
                
                # Compare with local output
                if os.path.exists(local_output):
                    print(f"\n   🔍 Comparison API vs Local:")
                    
                    # Convert API result to same format as CSV
                    api_lines = ["Timestamp,Demand\n"]
                    for item in api_result:
                        timestamp = f"{item['Date']} {item['Time']}"
                        demand = item['Demand']
                        api_lines.append(f"{timestamp},{demand}\n")
                    
                    # Compare first few lines
                    matches = 0
                    for i in range(1, min(6, len(local_lines), len(api_lines))):
                        local_line = local_lines[i].strip()
                        api_line = api_lines[i].strip()
                        
                        if local_line == api_line:
                            matches += 1
                            print(f"      ✅ Line {i} matches: {local_line}")
                        else:
                            print(f"      ❌ Line {i} differs:")
                            print(f"         Local: {local_line}")
                            print(f"         API:   {api_line}")
                    
                    if matches == 5:
                        print(f"      ✅ API and Local outputs are IDENTICAL!")
                        return True
                    else:
                        print(f"      ❌ API and Local outputs differ")
                        return False
                
            else:
                print(f"   ❌ API processing failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error testing API function: {e}")
            import traceback
            print(f"   Traceback: {traceback.format_exc()}")
            return False
        
    except subprocess.TimeoutExpired:
        print(f"❌ Local processing timed out")
        return False
    except Exception as e:
        print(f"❌ Error in testing: {e}")
        return False

def compare_with_existing_outputs():
    """Compare with the existing problematic outputs"""
    print(f"\n📊 COMPARING WITH EXISTING OUTPUTS")
    print("=" * 60)
    
    files_to_check = [
        ("my_results6.csv", "Local main_s3.py output (CORRECT)"),
        ("api_output.csv", "API output (WRONG - before fix)")
    ]
    
    for filename, description in files_to_check:
        if os.path.exists(filename):
            print(f"\n📁 {description}:")
            print(f"   File: {filename}")
            
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            print(f"   Lines: {len(lines)}")
            print(f"   Sample data:")
            for i in range(1, min(4, len(lines))):
                print(f"      {lines[i].strip()}")
        else:
            print(f"\n⚠️ {description}: File not found ({filename})")

def main():
    """Run all tests to verify the API fix"""
    print("🔧 TESTING API FIX FOR CONSISTENCY")
    print("=" * 70)
    
    # Show existing outputs first
    compare_with_existing_outputs()
    
    # Test the fix
    success = test_api_vs_local()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ API FIX SUCCESSFUL!")
        print("\n🎯 Results:")
        print("  • API and local main_s3.py now produce IDENTICAL outputs")
        print("  • Data preservation is working correctly in API")
        print("  • Values are preserved exactly as in input file")
        print("  • Time progression is correct (hourly increments)")
        print("\n🚀 API is now ready for deployment!")
    else:
        print("❌ API FIX FAILED!")
        print("\n🔍 Issues found:")
        print("  • API and local outputs still differ")
        print("  • Check the comparison details above")
        print("  • Further investigation needed")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
