import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "app"))

# Import the necessary modules
from services import process_metadata

def test_process_metadata():
    """
    Test the process_metadata function directly
    """
    # Get input file, output path, and metadata parameters from command line arguments or use defaults
    if len(sys.argv) > 5:
        input_file = sys.argv[1]
        output_path = sys.argv[2]
        country_code = sys.argv[3]
        industry_type = sys.argv[4]
        sub_industry_type = sys.argv[5]
    else:
        # Default test values
        input_file = "format8_unix.csv"  # Just the file key, not the full S3 URL
        output_path = "output/"  # Output path in the output bucket
        country_code = "IN"  # India
        industry_type = "Commercial"
        sub_industry_type = "Data-center"

    print(f"Testing with input_file: {input_file}, output_path: {output_path}")
    print(f"Metadata: country_code={country_code}, industry_type={industry_type}, sub_industry_type={sub_industry_type}")

    # Call the function
    success, message, output_file, timezone = process_metadata(
        input_file,
        output_path,
        country_code,
        industry_type,
        sub_industry_type
    )

    # Print the results
    print(f"Success: {success}")
    print(f"Message: {message}")
    print(f"Output file: {output_file}")
    print(f"Timezone: {timezone}")

    return success

if __name__ == "__main__":
    test_process_metadata()
