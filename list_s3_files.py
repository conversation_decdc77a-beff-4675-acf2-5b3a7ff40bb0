import os
import logging
from dotenv import load_dotenv
from s3_file_handler import S3FileHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """
    List files in the S3 bucket
    """
    # Get bucket name from environment variable
    bucket_name = os.getenv('BUCKET_NAME')
    logger.info(f"Using S3 bucket: {bucket_name}")
    
    # Initialize S3 file handler
    s3_handler = S3FileHandler()
    
    # List files in the bucket
    files = s3_handler.list_files(verbose=True)
    
    logger.info(f"Found {len(files)} files in S3 bucket {s3_handler.bucket_name}")
    
if __name__ == "__main__":
    main()
