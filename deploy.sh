#!/bin/bash

# Exit on error
set -e

# Update system packages
echo "Updating system packages..."
sudo apt update
sudo apt upgrade -y

# Install required packages
echo "Installing required packages..."
sudo apt install -y python3-pip python3-venv nginx

# Create a virtual environment
echo "Setting up Python virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Copy the systemd service file
echo "Setting up systemd service..."
sudo cp demand-api.service /etc/systemd/system/

# Copy the Nginx configuration
echo "Setting up Nginx configuration..."
sudo cp nginx-demand-api /etc/nginx/sites-available/demand-api
if [ ! -f /etc/nginx/sites-enabled/demand-api ]; then
   sudo ln -s /etc/nginx/sites-available/demand-api /etc/nginx/sites-enabled/
fi

# Reload systemd and start the service
echo "Starting services..."
sudo systemctl daemon-reload
sudo systemctl enable demand-api
sudo systemctl start demand-api

# Restart Nginx
sudo nginx -t
sudo systemctl restart nginx

echo "Deployment completed!"
echo "You can now access the API at http://your-domain.com"
echo "To set up SSL with Certbot, run:"
echo "sudo apt install -y certbot python3-certbot-nginx"
echo "sudo certbot --nginx -d your-domain.com"
