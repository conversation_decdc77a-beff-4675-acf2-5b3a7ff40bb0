#!/usr/bin/env python3
"""
Test script for the specific problematic files
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_csv_file():
    """Analyze the CSV file with date-only format"""
    print("📊 ANALYZING CSV FILE: manyata copy(Sheet2) 1.csv")
    print("=" * 60)
    
    csv_file = "manyata copy(Sheet2) 1.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    # Read the file manually (no pandas dependency)
    with open(csv_file, 'r') as f:
        lines = f.readlines()
    
    print(f"📁 File: {csv_file}")
    print(f"📊 Total lines: {len(lines)} (including header)")
    print(f"📊 Data points: {len(lines) - 1}")
    
    # Parse header
    header = lines[0].strip().split(',')
    print(f"📋 Header: {header}")
    
    # Analyze first few data points
    print(f"\n📖 First 10 data points:")
    dates_seen = {}
    for i in range(1, min(11, len(lines))):
        parts = lines[i].strip().split(',')
        if len(parts) >= 2:
            date_str = parts[0]
            demand_val = parts[1]
            print(f"   {i}. {date_str} -> {demand_val}")
            
            # Count occurrences of each date
            if date_str in dates_seen:
                dates_seen[date_str] += 1
            else:
                dates_seen[date_str] = 1
    
    # Analyze date pattern
    print(f"\n📅 Date pattern analysis:")
    unique_dates = set()
    date_counts = {}
    
    for i in range(1, len(lines)):
        parts = lines[i].strip().split(',')
        if len(parts) >= 2:
            date_str = parts[0]
            unique_dates.add(date_str)
            if date_str in date_counts:
                date_counts[date_str] += 1
            else:
                date_counts[date_str] = 1
    
    print(f"   Unique dates: {len(unique_dates)}")
    print(f"   Total data points: {len(lines) - 1}")
    
    if unique_dates:
        avg_points_per_date = (len(lines) - 1) / len(unique_dates)
        print(f"   Average points per date: {avg_points_per_date:.1f}")
        
        # Check if it's hourly data (24 points per day)
        if abs(avg_points_per_date - 24) < 0.1:
            print(f"   ✅ DETECTED: Hourly data format (24 points per day)")
        else:
            print(f"   ⚠️ UNUSUAL: Not standard hourly format")
    
    # Check for leap year issue
    first_date = list(unique_dates)[0] if unique_dates else None
    if first_date:
        try:
            # Parse the first date to get the year
            if '/' in first_date:
                parts = first_date.split('/')
                if len(parts) == 3:
                    month, day, year = parts
                    if len(year) == 4:
                        year_int = int(year)
                    else:
                        year_int = 2000 + int(year) if int(year) < 50 else 1900 + int(year)
                    
                    print(f"\n🗓️ Year analysis:")
                    print(f"   Detected year: {year_int}")
                    
                    # Check if it's a leap year
                    import calendar
                    is_leap = calendar.isleap(year_int)
                    expected_days = 366 if is_leap else 365
                    expected_hours = expected_days * 24
                    
                    print(f"   Is leap year: {is_leap}")
                    print(f"   Expected days: {expected_days}")
                    print(f"   Expected hours: {expected_hours}")
                    print(f"   Actual data points: {len(lines) - 1}")
                    
                    if is_leap and (len(lines) - 1) == 8760:
                        print(f"   ❌ PROBLEM: Leap year but only 8760 hours (missing 24 hours of Feb 29!)")
                    elif is_leap and (len(lines) - 1) == 8784:
                        print(f"   ✅ CORRECT: Leap year with 8784 hours")
                    elif not is_leap and (len(lines) - 1) == 8760:
                        print(f"   ✅ CORRECT: Regular year with 8760 hours")
                    else:
                        print(f"   ⚠️ UNUSUAL: Unexpected number of data points")
        
        except Exception as e:
            print(f"   ❌ Error parsing date: {e}")
    
    # Check for February 29th
    feb_29_count = 0
    for line in lines[1:]:
        if '2/29/' in line or '29/2/' in line:
            feb_29_count += 1
    
    print(f"\n📅 February 29th check:")
    print(f"   February 29th entries: {feb_29_count}")
    
    if feb_29_count == 0:
        print(f"   ❌ NO February 29th data found!")
    elif feb_29_count == 24:
        print(f"   ✅ Complete February 29th data (24 hours)")
    else:
        print(f"   ⚠️ Partial February 29th data ({feb_29_count} hours)")
    
    return True

def test_csv_processing():
    """Test processing the CSV file"""
    print(f"\n🔄 TESTING CSV FILE PROCESSING")
    print("=" * 50)
    
    csv_file = "manyata copy(Sheet2) 1.csv"
    
    try:
        # Test with preservation mode
        from app.services import process_s3_file_preserve_original
        from llm_handler import LLMHandler
        
        # LLM configuration
        LLM_CONFIG = {
            "api_base": "http://*************:11435/v1",
            "api_key": "dummy-key", 
            "model_name": "gemma3:12b",
            "temperature": 0
        }
        
        llm_handler = LLMHandler(LLM_CONFIG)
        
        print(f"   🔄 Processing with preservation mode...")
        result = process_s3_file_preserve_original(csv_file, llm_handler, bucket_name=None)
        
        if result:
            print(f"   ✅ Processed {len(result)} data points")
            
            # Show sample results
            print(f"\n   📊 Sample processed data:")
            for i in range(min(5, len(result))):
                item = result[i]
                print(f"   {i+1}. {item}")
            
            # Check for February 29th in results
            feb_29_results = [item for item in result if '29/02/' in item.get('Date', '') or '02/29/' in item.get('Date', '')]
            print(f"\n   📅 February 29th in results: {len(feb_29_results)} entries")
            
            if feb_29_results:
                print(f"   Sample Feb 29 data:")
                for i, item in enumerate(feb_29_results[:3]):
                    print(f"   {i+1}. {item}")
        else:
            print(f"   ❌ Processing failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error processing CSV: {e}")
        return False

def analyze_excel_file():
    """Analyze the Excel file with year conversion issue"""
    print(f"\n📊 ANALYZING EXCEL FILE: manyata 13 1.xlsx")
    print("=" * 60)
    
    excel_file = "manyata 13 1.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        return False
    
    print(f"📁 File: {excel_file}")
    print(f"📅 Expected year from filename: 2023")
    
    # Try to read without pandas
    print(f"⚠️ Cannot analyze Excel file without pandas/openpyxl")
    print(f"   But we can test the processing pipeline...")
    
    return True

def test_excel_processing():
    """Test processing the Excel file"""
    print(f"\n🔄 TESTING EXCEL FILE PROCESSING")
    print("=" * 50)
    
    excel_file = "manyata 13 1.xlsx"
    
    try:
        # Test with preservation mode
        from app.services import process_s3_file_preserve_original
        from llm_handler import LLMHandler
        
        # LLM configuration
        LLM_CONFIG = {
            "api_base": "http://*************:11435/v1",
            "api_key": "dummy-key", 
            "model_name": "gemma3:12b",
            "temperature": 0
        }
        
        llm_handler = LLMHandler(LLM_CONFIG)
        
        print(f"   🔄 Processing with preservation mode...")
        result = process_s3_file_preserve_original(excel_file, llm_handler, bucket_name=None)
        
        if result:
            print(f"   ✅ Processed {len(result)} data points")
            
            # Show sample results
            print(f"\n   📊 Sample processed data:")
            for i in range(min(5, len(result))):
                item = result[i]
                print(f"   {i+1}. {item}")
            
            # Check years in results
            years_found = set()
            for item in result[:100]:  # Check first 100 items
                date_str = item.get('Date', '')
                if '/' in date_str:
                    parts = date_str.split('/')
                    if len(parts) >= 3:
                        year_part = parts[2] if len(parts[2]) == 4 else parts[0]
                        try:
                            if len(year_part) == 4:
                                years_found.add(int(year_part))
                            elif len(year_part) == 2:
                                year_int = 2000 + int(year_part) if int(year_part) < 50 else 1900 + int(year_part)
                                years_found.add(year_int)
                        except:
                            pass
            
            print(f"\n   📅 Years found in results: {sorted(years_found)}")
            
            if 2025 in years_found and 2023 not in years_found:
                print(f"   ❌ YEAR CONVERSION PROBLEM: 2023 → 2025")
            elif 2023 in years_found:
                print(f"   ✅ Year preserved correctly: 2023")
            else:
                print(f"   ⚠️ Unexpected years found")
        else:
            print(f"   ❌ Processing failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error processing Excel: {e}")
        return False

def main():
    """Run all tests for the specific problematic files"""
    print("🔍 TESTING SPECIFIC PROBLEMATIC FILES")
    print("=" * 70)
    
    success = True
    
    # Test 1: Analyze CSV file
    if not analyze_csv_file():
        success = False
    
    # Test 2: Process CSV file
    if not test_csv_processing():
        success = False
    
    # Test 3: Analyze Excel file
    if not analyze_excel_file():
        success = False
    
    # Test 4: Process Excel file
    if not test_excel_processing():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ TESTING COMPLETED!")
        print("\nKey issues identified:")
        print("  • CSV file: Date-only format with missing Feb 29 data")
        print("  • Excel file: Year conversion 2023 → 2025")
        print("  • Both files: Values being modified")
    else:
        print("❌ TESTING FAILED!")
        print("Check the error messages above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
