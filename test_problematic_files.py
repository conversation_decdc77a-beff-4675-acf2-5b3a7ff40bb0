#!/usr/bin/env python3
"""
Test script for the two problematic files with comprehensive analysis
"""

import os
import sys
import logging
from datetime import datetime

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_csv_file_with_preservation():
    """Test the CSV file with date-only format using preservation mode"""
    print("📊 TESTING CSV FILE: manyata copy(Sheet2) 1.csv")
    print("=" * 60)
    
    csv_file = "manyata copy(Sheet2) 1.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    try:
        # Use preservation mode to avoid value changes
        from app.services import process_s3_file_preserve_original
        from llm_handler import LLMHandler
        
        # LLM configuration
        LLM_CONFIG = {
            "api_base": "http://*************:11435/v1",
            "api_key": "dummy-key", 
            "model_name": "gemma3:12b",
            "temperature": 0
        }
        
        llm_handler = LLMHandler(LLM_CONFIG)
        
        print(f"🔄 Processing with PRESERVATION MODE...")
        start_time = datetime.now()
        
        result = process_s3_file_preserve_original(csv_file, llm_handler, bucket_name=None)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        if result:
            print(f"✅ Processed {len(result)} data points in {processing_time:.2f} seconds")
            
            # Analyze the results
            print(f"\n📊 RESULTS ANALYSIS:")
            
            # Check years
            years_found = set()
            for item in result[:100]:
                date_str = item.get('Date', '')
                if '/' in date_str:
                    parts = date_str.split('/')
                    if len(parts) >= 3:
                        year_part = parts[2] if len(parts[2]) >= 2 else parts[0]
                        try:
                            if len(year_part) == 4:
                                years_found.add(int(year_part))
                            elif len(year_part) == 2:
                                year_int = 2000 + int(year_part) if int(year_part) <= 30 else 1900 + int(year_part)
                                years_found.add(year_int)
                        except:
                            pass
            
            print(f"   Years found: {sorted(years_found)}")
            
            # Check for leap year handling
            if 2024 in years_found:
                print(f"   ✅ 2024 detected (leap year)")
                if len(result) == 8784:
                    print(f"   ✅ CORRECT: 8784 data points for leap year 2024")
                elif len(result) == 8760:
                    print(f"   ❌ PROBLEM: Only 8760 data points (missing 24 hours of Feb 29)")
                else:
                    print(f"   ⚠️ UNUSUAL: {len(result)} data points")
            
            # Check for February 29th
            feb_29_count = sum(1 for item in result if '29/02/' in item.get('Date', '') or '02/29/' in item.get('Date', ''))
            print(f"   February 29th entries: {feb_29_count}")
            
            if feb_29_count == 24:
                print(f"   ✅ Complete February 29th data")
            elif feb_29_count == 0:
                print(f"   ❌ Missing February 29th data")
            else:
                print(f"   ⚠️ Partial February 29th data")
            
            # Show sample data
            print(f"\n📋 Sample processed data:")
            for i in range(min(5, len(result))):
                item = result[i]
                print(f"   {i+1}. {item}")
            
            # Check for value preservation
            print(f"\n💰 VALUE PRESERVATION CHECK:")
            print(f"   Original CSV values (first 5): Need to read CSV manually")
            print(f"   Processed values (first 5): {[item['Demand'] for item in result[:5]]}")
            
            return True
        else:
            print(f"❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_excel_file_with_preservation():
    """Test the Excel file with year conversion issue using preservation mode"""
    print(f"\n📊 TESTING EXCEL FILE: manyata 13 1.xlsx")
    print("=" * 60)
    
    excel_file = "manyata 13 1.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        return False
    
    try:
        # Use preservation mode to avoid value changes
        from app.services import process_s3_file_preserve_original
        from llm_handler import LLMHandler
        
        # LLM configuration
        LLM_CONFIG = {
            "api_base": "http://*************:11435/v1",
            "api_key": "dummy-key", 
            "model_name": "gemma3:12b",
            "temperature": 0
        }
        
        llm_handler = LLMHandler(LLM_CONFIG)
        
        print(f"🔄 Processing with PRESERVATION MODE...")
        print(f"📅 Expected year: 2023 (from filename)")
        
        start_time = datetime.now()
        
        result = process_s3_file_preserve_original(excel_file, llm_handler, bucket_name=None)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        if result:
            print(f"✅ Processed {len(result)} data points in {processing_time:.2f} seconds")
            
            # Analyze the results
            print(f"\n📊 RESULTS ANALYSIS:")
            
            # Check years
            years_found = set()
            for item in result[:100]:
                date_str = item.get('Date', '')
                if '/' in date_str:
                    parts = date_str.split('/')
                    if len(parts) >= 3:
                        year_part = parts[2] if len(parts[2]) >= 2 else parts[0]
                        try:
                            if len(year_part) == 4:
                                years_found.add(int(year_part))
                            elif len(year_part) == 2:
                                year_int = 2000 + int(year_part) if int(year_part) <= 30 else 1900 + int(year_part)
                                years_found.add(year_int)
                        except:
                            pass
            
            print(f"   Years found: {sorted(years_found)}")
            
            # Check for year conversion issue
            if 2025 in years_found and 2023 not in years_found:
                print(f"   ❌ YEAR CONVERSION PROBLEM: 2023 → 2025")
            elif 2023 in years_found:
                print(f"   ✅ Year preserved correctly: 2023")
            else:
                print(f"   ⚠️ Unexpected years: {years_found}")
            
            # Check for leap year detection
            if 2023 in years_found:
                print(f"   ✅ 2023 detected (regular year)")
                if len(result) == 8760:
                    print(f"   ✅ CORRECT: 8760 data points for regular year 2023")
                elif len(result) == 8784:
                    print(f"   ❌ PROBLEM: 8784 data points (incorrectly treated as leap year)")
                else:
                    print(f"   ⚠️ UNUSUAL: {len(result)} data points")
            
            # Show sample data
            print(f"\n📋 Sample processed data:")
            for i in range(min(5, len(result))):
                item = result[i]
                print(f"   {i+1}. {item}")
            
            return True
        else:
            print(f"❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_format_detection():
    """Test format detection for both files"""
    print(f"\n🔍 TESTING FORMAT DETECTION")
    print("=" * 50)
    
    files_to_test = [
        "manyata copy(Sheet2) 1.csv",
        "manyata 13 1.xlsx"
    ]
    
    for file_name in files_to_test:
        if not os.path.exists(file_name):
            print(f"⚠️ Skipping {file_name} (not found)")
            continue
        
        print(f"\n📁 Testing: {file_name}")
        
        try:
            from s3_file_handler import S3FileHandler
            from file_processor import FileProcessor
            
            # Read the file
            handler = S3FileHandler()
            df, file_type = handler.read_file(file_name)
            
            print(f"   File type: {file_type}")
            print(f"   Shape: {df.shape}")
            print(f"   Columns: {list(df.columns)}")
            
            # Test format detection
            processor = FileProcessor()
            detected_format = processor.identify_format(df)
            
            print(f"   Detected format: {detected_format}")
            
            # Expected formats
            if "csv" in file_name.lower():
                if detected_format == "date_only":
                    print(f"   ✅ CORRECT: Date-only format detected for CSV")
                else:
                    print(f"   ❌ WRONG: Expected 'date_only', got '{detected_format}'")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Run all tests for the problematic files"""
    print("🔍 COMPREHENSIVE TESTING OF PROBLEMATIC FILES")
    print("=" * 70)
    
    success = True
    
    # Test 1: Format detection
    test_format_detection()
    
    # Test 2: CSV file processing
    if not test_csv_file_with_preservation():
        success = False
    
    # Test 3: Excel file processing
    if not test_excel_file_with_preservation():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ ALL TESTS COMPLETED!")
        print("\nKey findings:")
        print("  • CSV file: Date-only format with leap year handling")
        print("  • Excel file: Year conversion and value preservation")
        print("  • Both files: Using preservation mode to maintain data integrity")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Check the error messages above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
