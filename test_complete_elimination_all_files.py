#!/usr/bin/env python3
"""
Test complete elimination of data modification across ALL files
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_all_modifications_eliminated():
    """Test that ALL data modification functions are eliminated"""
    print("🔥 TESTING COMPLETE ELIMINATION ACROSS ALL FILES")
    print("=" * 70)
    
    input_file = "manyata 13 1.xlsx"
    output_file = "test_all_eliminated.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test with smart mode (should preserve if complete, complete if incomplete)
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--smart_mode",
        "--output_file", output_file,
        "--overwrite"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
            
            print(f"✅ Processing completed: {len(lines)} lines")
            print(f"Sample data:")
            for i in range(1, min(6, len(lines))):
                print(f"   {lines[i].strip()}")
            
            # Check for elimination indicators
            elimination_checks = []
            
            # 1. Check for preservation messages
            preservation_messages = [
                "OUTLIER DETECTION DISABLED",
                "MAGNITUDE DETECTION DISABLED", 
                "CUMULATIVE DATA CONVERSION DISABLED",
                "DATA GENERATION DISABLED",
                "PRECISION PRESERVATION ENABLED",
                "ALL DATA MODIFICATION FUNCTIONS DISABLED"
            ]
            
            found_messages = []
            for msg in preservation_messages:
                if msg in result.stdout:
                    found_messages.append(msg)
                    elimination_checks.append(f"msg_{msg.lower().replace(' ', '_')}")
            
            print(f"\n🔒 PRESERVATION MESSAGES FOUND:")
            for msg in found_messages:
                print(f"   ✅ {msg}")
            
            missing_messages = [msg for msg in preservation_messages if msg not in found_messages]
            if missing_messages:
                print(f"\n❌ MISSING PRESERVATION MESSAGES:")
                for msg in missing_messages:
                    print(f"   ❌ {msg}")
            
            # 2. Check year format
            if len(lines) > 1:
                first_line = lines[1].strip()
                if "2023" in first_line:
                    print(f"   ✅ YEAR: Correct (2023)")
                    elimination_checks.append("year_correct")
                elif "2025" in first_line or "/25 " in first_line:
                    print(f"   ❌ YEAR: Wrong (2025)")
                else:
                    print(f"   ⚠️ YEAR: Format unclear")
            
            # 3. Check if values look preserved (not synthetic)
            if len(lines) > 5:
                values = []
                for i in range(1, min(6, len(lines))):
                    parts = lines[i].strip().split(',')
                    if len(parts) >= 2:
                        try:
                            values.append(float(parts[1]))
                        except:
                            pass
                
                if values:
                    # Check precision (preserved values often have more decimals)
                    high_precision_count = sum(1 for v in values if '.' in str(v) and len(str(v).split('.')[1]) > 2)
                    
                    if high_precision_count > 0:
                        print(f"   ✅ VALUES: High precision preserved ({high_precision_count}/{len(values)} values)")
                        elimination_checks.append("precision_preserved")
                    
                    # Check if values are NOT in the synthetic range we saw before
                    synthetic_range_count = sum(1 for v in values if 50 <= v <= 80)
                    if synthetic_range_count < len(values):
                        print(f"   ✅ VALUES: Not synthetic pattern")
                        elimination_checks.append("not_synthetic")
                    else:
                        print(f"   ❌ VALUES: Still in synthetic range (50-80)")
            
            # 4. Check data count decision
            data_count = len(lines) - 1
            if "SMART PRESERVATION" in result.stdout:
                print(f"   ✅ DECISION: Smart preservation chosen ({data_count} points)")
                elimination_checks.append("smart_preservation")
            elif "SMART COMPLETION" in result.stdout:
                print(f"   ✅ DECISION: Smart completion chosen ({data_count} points)")
                elimination_checks.append("smart_completion")
            
            print(f"\n🎯 ELIMINATION ASSESSMENT:")
            print(f"   Success indicators: {len(elimination_checks)}")
            print(f"   Indicators found: {elimination_checks}")
            print(f"   Preservation messages: {len(found_messages)}/{len(preservation_messages)}")
            
            if len(elimination_checks) >= 5 and len(found_messages) >= 4:
                print(f"   ✅ COMPLETE ELIMINATION SUCCESSFUL!")
                return True
            else:
                print(f"   ❌ ELIMINATION INCOMPLETE - some modifications still active")
                return False
        else:
            print(f"❌ Processing failed")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Processing timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def compare_with_previous_outputs():
    """Compare with previous problematic outputs"""
    print(f"\n📊 COMPARING WITH PREVIOUS OUTPUTS")
    print("=" * 70)
    
    files_to_compare = [
        ("my_results7.csv", "Previous attempt (WRONG)"),
        ("my_results8.csv", "Recent attempt (STILL WRONG)"),
        ("test_all_eliminated.csv", "Complete elimination (should be CORRECT)")
    ]
    
    for filename, description in files_to_compare:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            print(f"\n📁 {description}:")
            print(f"   File: {filename}")
            print(f"   Lines: {len(lines)}")
            
            if len(lines) > 1:
                first_line = lines[1].strip()
                print(f"   First data: {first_line}")
                
                # Extract components
                parts = first_line.split(',')
                if len(parts) >= 2:
                    date_part = parts[0]
                    value_part = parts[1]
                    
                    # Check year
                    if "2023" in date_part:
                        print(f"   Year: ✅ 2023")
                    elif "2025" in date_part or "/25 " in date_part:
                        print(f"   Year: ❌ 2025")
                    else:
                        print(f"   Year: ⚠️ Unknown")
                    
                    # Check value characteristics
                    try:
                        value = float(value_part)
                        decimal_places = len(value_part.split('.')[1]) if '.' in value_part else 0
                        
                        if decimal_places > 2:
                            print(f"   Precision: ✅ High ({decimal_places} decimals)")
                        elif decimal_places == 2:
                            print(f"   Precision: ❌ Rounded (2 decimals)")
                        else:
                            print(f"   Precision: ⚠️ Integer")
                        
                        if 50 <= value <= 80:
                            print(f"   Pattern: ❌ Synthetic range ({value})")
                        else:
                            print(f"   Pattern: ✅ Likely original ({value})")
                    except:
                        print(f"   Value: ⚠️ Cannot parse")
        else:
            print(f"\n⚠️ {description}: File not found ({filename})")

def show_elimination_summary():
    """Show what was eliminated across all files"""
    print(f"\n🔥 COMPLETE ELIMINATION SUMMARY")
    print("=" * 70)
    
    print(f"📁 FILES MODIFIED:")
    print(f"   • main_s3.py: Smart processing + preservation flags")
    print(f"   • data_processor.py: Disabled outlier detection, magnitude scaling, cumulative conversion")
    print(f"   • output_generator.py: Disabled value rounding")
    
    print(f"\n🚫 FUNCTIONS ELIMINATED:")
    print(f"   • detect_and_handle_outliers(): No outlier modification")
    print(f"   • _detect_magnitude(): No unit scaling")
    print(f"   • handle_cumulative_data(): No cumulative conversion")
    print(f"   • generate_full_year_data(): No synthetic data generation")
    print(f"   • round(demand, 2): No precision rounding")
    print(f"   • Random variation: No ±5% noise addition")
    
    print(f"\n✅ PRESERVATION FEATURES:")
    print(f"   • Original values: Preserved exactly")
    print(f"   • Original precision: Maintained (no rounding)")
    print(f"   • Original timestamps: Preserved")
    print(f"   • Year format: Fixed (2023 not 2025)")
    print(f"   • Smart analysis: Preserve complete, complete incomplete")
    
    print(f"\n🎯 EXPECTED RESULTS:")
    print(f"   • Year: 2023 (correct)")
    print(f"   • Values: Original precision preserved")
    print(f"   • Count: Based on completeness analysis")
    print(f"   • Processing: Fast (no unnecessary modifications)")

def main():
    """Run complete elimination test across all files"""
    print("🔥 COMPLETE ELIMINATION TEST - ALL FILES")
    print("=" * 70)
    
    success = test_all_modifications_eliminated()
    compare_with_previous_outputs()
    show_elimination_summary()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ COMPLETE ELIMINATION SUCCESSFUL!")
        print("\n🎯 All data modification functions eliminated:")
        print("  • data_processor.py: All modification functions disabled")
        print("  • output_generator.py: Rounding disabled")
        print("  • main_s3.py: Smart processing with preservation")
        print("  • Year conversion: Fixed")
        print("  • Value preservation: Complete")
        print("\n🚀 Ready to apply same fixes to API!")
    else:
        print("❌ ELIMINATION INCOMPLETE!")
        print("\n🔍 Still need to fix:")
        print("  • Check which modification functions are still active")
        print("  • Verify preservation flags are working")
        print("  • Ensure all files respect preservation mode")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
