import pandas as pd
import numpy as np
from datetime import datetime
import re
from typing import Union, List, Dict, Any, Optional
import logging
from date_format_detector import DateFormatDetector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TimestampHandler:
    """
    Handles different timestamp formats and standardization
    """

    def __init__(self, detected_format: str = None):
        """
        Initialize the TimestampHandler

        Args:
            detected_format: Detected date format ('DMY', 'MDY', 'YMD', or None)
        """
        self.detected_format = detected_format
        self.format_detector = DateFormatDetector()

        # Initialize format lists
        if detected_format:
            logger.info(f"Using detected date format: {detected_format}")
            # Use prioritized formats based on detection
            self.timestamp_formats = self.format_detector.get_datetime_format(detected_format)
            self.date_formats = self.format_detector.get_date_format(detected_format)
        else:
            # Default formats (backward compatibility)
            self.timestamp_formats = [
                '%Y-%m-%d %H:%M:%S',  # 2025-01-01 00:00:00
                '%Y-%m-%d %H:%M',     # 2025-01-01 00:00
                '%Y-%m-%dT%H:%M:%S',  # 2025-01-01T00:00:00
                '%Y-%m-%dT%H:%M',     # 2025-01-01T00:00
                '%m/%d/%y %H:%M:%S',  # 1/1/25 00:00:00
                '%m/%d/%y %H:%M',     # 1/1/25 00:00
                '%m-%d-%Y %H:%M:%S',  # 01-01-2025 00:00:00
                '%m-%d-%Y %H:%M',     # 01-01-2025 00:00
                '%d-%m-%Y %H:%M:%S',  # 01-01-2025 00:00:00
                '%d-%m-%Y %H:%M',     # 01-01-2025 00:00
                '%m/%d/%Y %H:%M:%S',  # 1/1/2025 00:00:00
                '%m/%d/%Y %H:%M',     # 1/1/2025 00:00
                '%d/%m/%Y %H:%M:%S',  # 01/01/2025 00:00:00
                '%d/%m/%Y %H:%M',     # 01/01/2025 00:00
                '%Y%m%d%H%M%S',       # 20250101000000
                '%Y%m%d%H%M',         # 202501010000
            ]

            # Date-only formats
            self.date_formats = [
                '%Y-%m-%d',           # 2025-01-01
                '%m/%d/%y',           # 1/1/25
                '%m-%d-%Y',           # 01-01-2025
                '%d-%m-%Y',           # 01-01-2025
                '%m/%d/%Y',           # 1/1/2025
                '%d/%m/%Y',           # 01/01/2025
                '%Y%m%d',             # 20250101
            ]

        # Always keep ISO formats at the beginning for unambiguous formats
        self.timestamp_formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M'] + \
                               [fmt for fmt in self.timestamp_formats if fmt not in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M']]

        self.date_formats = ['%Y-%m-%d'] + [fmt for fmt in self.date_formats if fmt != '%Y-%m-%d']

    def parse_timestamp(self, timestamp: Union[str, int, float]) -> datetime:
        """
        Parse a timestamp in various formats

        Args:
            timestamp: The timestamp to parse

        Returns:
            Parsed datetime object
        """
        if isinstance(timestamp, (int, float)):
            # Try parsing as Unix timestamp
            try:
                # Check if timestamp is in seconds (10 digits) or milliseconds (13 digits)
                if len(str(int(timestamp))) > 10:
                    timestamp = timestamp / 1000  # Convert from milliseconds to seconds

                # Handle timestamps that might be in the future (2025+)
                try:
                    return datetime.fromtimestamp(timestamp)
                except (ValueError, OverflowError):
                    # Try interpreting as seconds since 2000-01-01 instead of 1970-01-01
                    base_date = datetime(2000, 1, 1)
                    return base_date + pd.Timedelta(seconds=int(timestamp))
            except (ValueError, OverflowError) as e:
                logger.warning(f"Failed to parse Unix timestamp: {timestamp} - {str(e)}")
                return None

        elif isinstance(timestamp, str):
            # Try parsing with various formats
            for fmt in self.timestamp_formats:
                try:
                    return datetime.strptime(timestamp, fmt)
                except ValueError:
                    continue

            # Try parsing date-only formats
            for fmt in self.date_formats:
                try:
                    return datetime.strptime(timestamp, fmt)
                except ValueError:
                    continue

            # Try parsing with pandas (which can handle more formats)
            try:
                return pd.to_datetime(timestamp).to_pydatetime()
            except:
                logger.warning(f"Failed to parse timestamp: {timestamp}")
                return None

        return None

    def standardize_timestamp(self, timestamp: datetime, hourly: bool = True) -> str:
        """
        Standardize a timestamp to a consistent format

        Args:
            timestamp: The datetime object to standardize
            hourly: Whether to round to the nearest hour

        Returns:
            Standardized timestamp string
        """
        if timestamp is None:
            return None

        if hourly:
            # Round to the nearest hour
            if timestamp.minute >= 30:
                timestamp = timestamp.replace(minute=0, second=0, microsecond=0)
                timestamp = timestamp.replace(hour=timestamp.hour + 1)
            else:
                timestamp = timestamp.replace(minute=0, second=0, microsecond=0)

        # Format as "MM/DD/YY HH:00"
        return timestamp.strftime('%m/%d/%y %H:%M')

    def create_hourly_timestamps(self, start_date: datetime, end_date: datetime) -> List[str]:
        """
        Create a list of hourly timestamps between start_date and end_date

        Args:
            start_date: The start date
            end_date: The end date

        Returns:
            List of standardized timestamp strings
        """
        # Round to hours
        start_date = start_date.replace(minute=0, second=0, microsecond=0)
        end_date = end_date.replace(minute=0, second=0, microsecond=0)

        # Create date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='H')

        # Convert to standardized format
        return [self.standardize_timestamp(dt.to_pydatetime()) for dt in date_range]

    def construct_timestamp_from_components(self, year=None, month=None, day=None, hour=None, minute=0, second=0) -> datetime:
        """
        Construct a timestamp from components

        Args:
            year: Year component
            month: Month component
            day: Day component
            hour: Hour component
            minute: Minute component
            second: Second component

        Returns:
            Constructed datetime object
        """
        try:
            return datetime(year=year, month=month, day=day, hour=hour, minute=minute, second=second)
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to construct timestamp from components: {e}")
            return None

    def extract_timestamp_components(self, timestamp_str: str) -> Dict[str, int]:
        """
        Extract components from a timestamp string using regex

        Args:
            timestamp_str: The timestamp string

        Returns:
            Dictionary of timestamp components
        """
        components = {}

        # Try to extract year, month, day
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY or DD/MM/YYYY
            r'(\d{1,2})-(\d{1,2})-(\d{4})',  # MM-DD-YYYY or DD-MM-YYYY
            r'(\d{1,2})/(\d{1,2})/(\d{2})',  # MM/DD/YY or DD/MM/YY
        ]

        for pattern in date_patterns:
            match = re.search(pattern, timestamp_str)
            if match:
                if len(match.group(3)) == 4:  # YYYY format
                    components['year'] = int(match.group(1))
                    components['month'] = int(match.group(2))
                    components['day'] = int(match.group(3))
                elif len(match.group(3)) == 2:  # YY format
                    components['month'] = int(match.group(1))
                    components['day'] = int(match.group(2))
                    components['year'] = 2000 + int(match.group(3))  # Assuming 21st century
                break

        # Try to extract hour, minute, second
        time_patterns = [
            r'(\d{1,2}):(\d{1,2}):(\d{1,2})',  # HH:MM:SS
            r'(\d{1,2}):(\d{1,2})',            # HH:MM
        ]

        for pattern in time_patterns:
            match = re.search(pattern, timestamp_str)
            if match:
                components['hour'] = int(match.group(1))
                components['minute'] = int(match.group(2))
                if len(match.groups()) > 2:
                    components['second'] = int(match.group(3))
                else:
                    components['second'] = 0
                break

        return components
