#!/usr/bin/env python3
"""
Debug script to identify why data values and years are being changed
"""

import os
import sys
import logging
from datetime import datetime

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_excel_file_processing():
    """Debug the Excel file processing to see where values and years change"""
    print("🔍 DEBUGGING DATA MODIFICATION ISSUES")
    print("=" * 60)
    
    excel_file = "manyata 13 1.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        return False
    
    print(f"📁 Debugging file: {excel_file}")
    
    try:
        # Step 1: Read raw Excel data
        print(f"\n📖 STEP 1: Reading raw Excel data")
        import pandas as pd
        df = pd.read_excel(excel_file)
        
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        print(f"   Data types: {df.dtypes.to_dict()}")
        
        # Show original data
        print(f"\n   Original data (first 10 rows):")
        for i in range(min(10, len(df))):
            date_val = df.iloc[i, 0]
            demand_val = df.iloc[i, 1]
            print(f"   {i+1}. {date_val} -> {demand_val} (types: {type(date_val)}, {type(demand_val)})")
        
        # Check years
        first_date = pd.to_datetime(df.iloc[0, 0])
        last_date = pd.to_datetime(df.iloc[-1, 0])
        print(f"\n   Date range: {first_date} to {last_date}")
        print(f"   Years: {first_date.year} to {last_date.year}")
        
        # Step 2: Test S3 file handler
        print(f"\n🔄 STEP 2: Testing S3 file handler")
        from s3_file_handler import S3FileHandler
        
        handler = S3FileHandler()
        processed_df, file_type = handler.read_file(excel_file)
        
        print(f"   File type detected: {file_type}")
        print(f"   Processed shape: {processed_df.shape}")
        print(f"   Processed columns: {list(processed_df.columns)}")
        
        # Show processed data
        print(f"\n   Processed data (first 10 rows):")
        for i in range(min(10, len(processed_df))):
            date_val = processed_df.iloc[i, 0]
            demand_val = processed_df.iloc[i, 1]
            print(f"   {i+1}. {date_val} -> {demand_val} (types: {type(date_val)}, {type(demand_val)})")
        
        # Check if years changed
        proc_first_date = pd.to_datetime(processed_df.iloc[0, 0])
        proc_last_date = pd.to_datetime(processed_df.iloc[-1, 0])
        print(f"\n   Processed date range: {proc_first_date} to {proc_last_date}")
        print(f"   Processed years: {proc_first_date.year} to {proc_last_date.year}")
        
        if first_date.year != proc_first_date.year:
            print(f"   ❌ YEAR CHANGED: {first_date.year} → {proc_first_date.year}")
        else:
            print(f"   ✅ Year preserved: {first_date.year}")
        
        # Step 3: Test format detection
        print(f"\n🔍 STEP 3: Testing format detection")
        from file_processor import FileProcessor
        
        processor = FileProcessor()
        format_type = processor.identify_format(processed_df)
        print(f"   Detected format: {format_type}")
        
        # Step 4: Test date format detection
        print(f"\n📅 STEP 4: Testing date format detection")
        from date_format_detector import DateFormatDetector
        
        detector = DateFormatDetector()
        date_format = detector.detect_date_format(processed_df.iloc[:, 0])
        print(f"   Detected date format: {date_format}")
        
        # Step 5: Test parser
        print(f"\n⚙️ STEP 5: Testing parser")
        from parsers import get_parser
        
        parser = get_parser(format_type)
        print(f"   Selected parser: {parser.__class__.__name__}")
        
        parsed_data = parser.parse(processed_df)
        print(f"   Parsed {len(parsed_data)} data points")
        
        # Show parsed data
        print(f"\n   Parsed data (first 10 points):")
        for i in range(min(10, len(parsed_data))):
            timestamp, demand = parsed_data[i]
            print(f"   {i+1}. {timestamp} -> {demand} (types: {type(timestamp)}, {type(demand)})")
        
        # Check if years changed during parsing
        if parsed_data:
            parsed_first_year = parsed_data[0][0].year
            parsed_last_year = parsed_data[-1][0].year
            print(f"\n   Parsed years: {parsed_first_year} to {parsed_last_year}")
            
            if first_date.year != parsed_first_year:
                print(f"   ❌ YEAR CHANGED DURING PARSING: {first_date.year} → {parsed_first_year}")
            else:
                print(f"   ✅ Year preserved during parsing: {first_date.year}")
        
        # Step 6: Test data processor
        print(f"\n🔄 STEP 6: Testing data processor")
        from data_processor import DataProcessor
        
        data_processor = DataProcessor()
        
        # Test with preservation mode
        print(f"\n   Testing with data preservation...")
        data_processor.preserve_original_precision = True
        
        processed_result = data_processor.process_data(parsed_data, 'MW', expand_to='year')
        print(f"   Processed to {len(processed_result)} data points")
        
        # Show processed result
        print(f"\n   Final processed data (first 10 points):")
        for i in range(min(10, len(processed_result))):
            item = processed_result[i]
            print(f"   {i+1}. {item}")
        
        # Check final years
        if processed_result:
            # Extract year from Date field
            first_date_str = processed_result[0]['Date']
            last_date_str = processed_result[-1]['Date']
            print(f"\n   Final date strings: {first_date_str} to {last_date_str}")
            
            # Parse to check years
            try:
                final_first_date = pd.to_datetime(first_date_str)
                final_last_date = pd.to_datetime(last_date_str)
                final_first_year = final_first_date.year
                final_last_year = final_last_date.year
                
                print(f"   Final years: {final_first_year} to {final_last_year}")
                
                if first_date.year != final_first_year:
                    print(f"   ❌ FINAL YEAR CHANGED: {first_date.year} → {final_first_year}")
                else:
                    print(f"   ✅ Final year preserved: {first_date.year}")
            except Exception as e:
                print(f"   ❌ Error parsing final dates: {e}")
        
        # Value comparison
        print(f"\n💰 VALUE COMPARISON:")
        original_values = df.iloc[:10, 1].values
        final_values = [float(item['Demand']) for item in processed_result[:10]]
        
        print(f"   Original values: {[float(v) for v in original_values]}")
        print(f"   Final values:    {final_values}")
        
        values_changed = False
        for i, (orig, final) in enumerate(zip(original_values, final_values)):
            if abs(float(orig) - float(final)) > 1e-10:
                print(f"   ❌ Value {i+1} changed: {orig} → {final}")
                values_changed = True
        
        if not values_changed:
            print(f"   ✅ All values preserved exactly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_timestamp_handler_issues():
    """Test the timestamp handler for year conversion issues"""
    print(f"\n🕐 TESTING TIMESTAMP HANDLER ISSUES")
    print("=" * 50)
    
    try:
        from timestamp_handler import TimestampHandler
        
        handler = TimestampHandler()
        
        # Test problematic date formats
        test_dates = [
            "1/1/23",      # Should be 2023, not 2025
            "12/31/23",    # Should be 2023, not 2025
            "1/1/2023",    # Should stay 2023
            "12/31/2023",  # Should stay 2023
        ]
        
        print(f"Testing timestamp parsing:")
        for date_str in test_dates:
            parsed = handler.parse_timestamp(date_str)
            if parsed:
                print(f"   '{date_str}' → {parsed} (year: {parsed.year})")
                
                # Check for incorrect year conversion
                if "23" in date_str and parsed.year != 2023:
                    print(f"   ❌ INCORRECT YEAR CONVERSION: Expected 2023, got {parsed.year}")
            else:
                print(f"   '{date_str}' → Failed to parse")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing timestamp handler: {e}")
        return False

def main():
    """Run all debugging tests"""
    print("🔍 COMPREHENSIVE DATA MODIFICATION DEBUGGING")
    print("=" * 70)
    
    success = True
    
    # Test 1: Excel file processing
    if not debug_excel_file_processing():
        success = False
    
    # Test 2: Timestamp handler
    if not test_timestamp_handler_issues():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ DEBUGGING COMPLETED!")
        print("\nKey findings will be shown above.")
        print("Look for ❌ markers to identify where data is being modified.")
    else:
        print("❌ DEBUGGING FAILED!")
        print("Check the error messages above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
