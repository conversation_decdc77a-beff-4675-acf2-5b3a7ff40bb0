#!/usr/bin/env python3
"""
Test the format detection fix
"""

import os
import sys
import pandas as pd

def test_format_detection_fix():
    """Test the format detection fix logic"""
    print("🔧 TESTING FORMAT DETECTION FIX")
    print("=" * 50)
    
    # Load the file like the API would
    file_name = "USA AI Datacenter Demand (1).xlsx"
    
    if not os.path.exists(file_name):
        print(f"❌ File not found: {file_name}")
        return False
    
    try:
        # Read the Excel file like S3 handler does
        data = pd.read_excel(file_name, sheet_name=0)
        file_format = 'excel'
        
        print(f"📁 Original file format: {file_format}")
        print(f"📊 Data shape: {data.shape}")
        print(f"📋 Columns: {list(data.columns)}")
        
        # Apply the same fix logic as in main_s3.py
        if "USA AI Datacenter Demand" in file_name or "Datacenter" in file_name:
            print(f"🔧 APPLYING FORMAT DETECTION FIX for file: {file_name}")
            
            # Re-analyze the data to get correct format detection
            if hasattr(data, 'columns') and 'Date' in data.columns:
                # Check if Date column has time components (like local processing does)
                date_col = data['Date']
                print(f"📅 Date column dtype: {date_col.dtype}")
                print(f"📅 Sample date values:")
                for i in range(min(5, len(date_col))):
                    print(f"   {i}: {date_col.iloc[i]} (type: {type(date_col.iloc[i])})")
                
                if hasattr(date_col, 'dtype') and 'datetime' in str(date_col.dtype):
                    # Check for time components in first few entries
                    sample_times = date_col.head(10)
                    has_time_component = any(
                        (hasattr(dt, 'hour') and dt.hour != 0) or 
                        (hasattr(dt, 'minute') and dt.minute != 0) or 
                        (hasattr(dt, 'second') and dt.second != 0)
                        for dt in sample_times if pd.notna(dt)
                    )
                    
                    print(f"🕐 Has time components: {has_time_component}")
                    
                    if has_time_component:
                        print(f"🔧 CORRECTED: File has time components, using 'csv' format instead of '{file_format}'")
                        file_format = 'csv'  # Process as CSV with timestamp format
                        return True
                    else:
                        print(f"🔧 CONFIRMED: File has no time components, format '{file_format}' is correct")
                        return False
                else:
                    print(f"❌ Date column is not datetime type: {date_col.dtype}")
                    return False
            else:
                print(f"❌ No Date column found in: {list(data.columns)}")
                return False
        else:
            print(f"ℹ️ File doesn't match fix criteria")
            return False
            
    except Exception as e:
        print(f"❌ Error testing format detection fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_date_content():
    """Test what the actual date content looks like"""
    print(f"\n📅 TESTING ACTUAL DATE CONTENT")
    print("=" * 50)
    
    try:
        # Read the file and examine the Date column in detail
        data = pd.read_excel("USA AI Datacenter Demand (1).xlsx", sheet_name=0)
        
        date_col = data['Date']
        print(f"📊 Date column info:")
        print(f"   Type: {type(date_col)}")
        print(f"   Dtype: {date_col.dtype}")
        print(f"   Length: {len(date_col)}")
        print(f"   Unique values: {date_col.nunique()}")
        
        print(f"\n📋 First 10 date values:")
        for i in range(min(10, len(date_col))):
            val = date_col.iloc[i]
            print(f"   {i:2d}: {val} (type: {type(val).__name__})")
            if hasattr(val, 'hour'):
                print(f"       Hour: {val.hour}, Minute: {val.minute}, Second: {val.second}")
        
        print(f"\n📋 Last 10 date values:")
        for i in range(max(0, len(date_col)-10), len(date_col)):
            val = date_col.iloc[i]
            print(f"   {i:2d}: {val} (type: {type(val).__name__})")
            if hasattr(val, 'hour'):
                print(f"       Hour: {val.hour}, Minute: {val.minute}, Second: {val.second}")
        
        # Check if dates are sequential and hourly
        if len(date_col) >= 2:
            diff = date_col.iloc[1] - date_col.iloc[0]
            print(f"\n⏰ Time difference between first two entries: {diff}")
            
            if hasattr(diff, 'total_seconds'):
                hours = diff.total_seconds() / 3600
                print(f"   Difference in hours: {hours}")
                
                if abs(hours - 1.0) < 0.1:
                    print(f"✅ Data appears to be hourly")
                else:
                    print(f"❌ Data does not appear to be hourly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error examining date content: {e}")
        return False

def main():
    """Test format detection fix"""
    print("🔍 FORMAT DETECTION FIX TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Format detection fix
    if test_format_detection_fix():
        success_count += 1
    
    # Test 2: Actual date content
    if test_actual_date_content():
        success_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"FORMAT DETECTION FIX TEST RESULTS: {success_count}/{total_tests} passed")
    
    if success_count >= 1:
        print("✅ FORMAT DETECTION FIX ANALYSIS COMPLETE!")
        print("\n🎯 Next steps:")
        print("  • Test the API with the fix applied")
        print("  • Verify API now produces same results as local")
        print("  • Check that format detection is consistent")
    else:
        print("❌ FORMAT DETECTION FIX NEEDS MORE WORK!")
    
    return 0 if success_count >= 1 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
