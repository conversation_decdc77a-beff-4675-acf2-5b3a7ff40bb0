import re
import logging
from datetime import datetime
from typing import List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DateFormatDetector:
    """
    Detects date formats in data files by analyzing patterns in date sequences
    """
    
    def __init__(self):
        """Initialize the DateFormatDetector"""
        # Common date format patterns
        self.date_patterns = {
            'DMY': r'(\d{1,2})[/\-\.](\d{1,2})[/\-\.](\d{2,4})',  # DD/MM/YYYY
            'MDY': r'(\d{1,2})[/\-\.](\d{1,2})[/\-\.](\d{2,4})',  # MM/DD/YYYY
            'YMD': r'(\d{2,4})[/\-\.](\d{1,2})[/\-\.](\d{1,2})',  # YYYY/MM/DD
        }
    
    def extract_dates(self, data, column_name=None) -> List[str]:
        """
        Extract date strings from data
        
        Args:
            data: DataFrame or list of dictionaries
            column_name: Name of the column containing dates (optional)
            
        Returns:
            List of date strings
        """
        dates = []
        
        try:
            # Handle DataFrame
            if hasattr(data, 'columns'):
                # If column name is provided, use it
                if column_name and column_name in data.columns:
                    dates = data[column_name].astype(str).tolist()
                else:
                    # Try to find date column
                    date_columns = [col for col in data.columns if 'date' in col.lower() or 'time' in col.lower()]
                    if date_columns:
                        dates = data[date_columns[0]].astype(str).tolist()
            
            # Handle list of dictionaries
            elif isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                # If column name is provided, use it
                if column_name:
                    dates = [str(item.get(column_name, '')) for item in data if column_name in item]
                else:
                    # Try to find date keys
                    date_keys = []
                    for key in data[0].keys():
                        if 'date' in key.lower() or 'time' in key.lower():
                            date_keys.append(key)
                    
                    if date_keys:
                        dates = [str(item.get(date_keys[0], '')) for item in data]
            
            # Filter out empty strings and None values
            dates = [date for date in dates if date and date.strip()]
            
            # Take only the first 10 dates for analysis
            return dates[:10]
            
        except Exception as e:
            logger.warning(f"Error extracting dates: {str(e)}")
            return []
    
    def parse_date_components(self, date_str: str) -> Optional[Tuple[int, int, int]]:
        """
        Parse a date string into day, month, year components
        
        Args:
            date_str: Date string
            
        Returns:
            Tuple of (first_component, second_component, third_component) or None if parsing fails
        """
        # Remove any time component
        date_part = date_str.split(' ')[0].split('T')[0]
        
        # Try different separators
        for separator in ['/', '-', '.']:
            if separator in date_part:
                parts = date_part.split(separator)
                if len(parts) == 3:
                    try:
                        return (int(parts[0]), int(parts[1]), int(parts[2]))
                    except ValueError:
                        continue
        
        return None
    
    def detect_format(self, dates: List[str]) -> str:
        """
        Detect the date format from a list of date strings
        
        Args:
            dates: List of date strings
            
        Returns:
            Detected format ('DMY', 'MDY', 'YMD', or 'unknown')
        """
        if not dates or len(dates) < 2:
            logger.warning("Not enough dates to detect format")
            return 'unknown'
        
        # Parse dates into components
        parsed_dates = []
        for date_str in dates:
            components = self.parse_date_components(date_str)
            if components:
                parsed_dates.append(components)
        
        if len(parsed_dates) < 2:
            logger.warning("Not enough parseable dates to detect format")
            return 'unknown'
        
        # Check for sequential patterns
        
        # Check if first component is incrementing (day in DMY)
        day_incrementing = True
        for i in range(1, len(parsed_dates)):
            if parsed_dates[i][0] != parsed_dates[i-1][0] + 1 or parsed_dates[i][1] != parsed_dates[i-1][1]:
                day_incrementing = False
                break
        
        # Check if second component is incrementing (day in MDY)
        month_incrementing = True
        for i in range(1, len(parsed_dates)):
            if parsed_dates[i][1] != parsed_dates[i-1][1] + 1 or parsed_dates[i][0] != parsed_dates[i-1][0]:
                month_incrementing = False
                break
        
        if day_incrementing:
            logger.info("Detected DMY format (day incrementing pattern)")
            return 'DMY'
        elif month_incrementing:
            logger.info("Detected MDY format (month incrementing pattern)")
            return 'MDY'
        
        # If no sequential pattern, check for values > 12 (must be days)
        for date in parsed_dates:
            if date[0] > 12:
                logger.info("Detected DMY format (first component > 12)")
                return 'DMY'
            if date[1] > 12:
                logger.info("Detected MDY format (second component > 12)")
                return 'MDY'
        
        # Check year component position
        if any(date[0] >= 2000 for date in parsed_dates):
            logger.info("Detected YMD format (first component is year)")
            return 'YMD'
        if any(date[2] >= 2000 for date in parsed_dates):
            # Check if first or second component is more likely to be month
            month_first = sum(1 <= date[0] <= 12 for date in parsed_dates)
            month_second = sum(1 <= date[1] <= 12 for date in parsed_dates)
            
            if month_first > month_second:
                logger.info("Detected MDY format (month in first position)")
                return 'MDY'
            else:
                logger.info("Detected DMY format (month in second position)")
                return 'DMY'
        
        # Default to DMY as it's common in many regions
        logger.info("No clear pattern, defaulting to DMY format")
        return 'DMY'
    
    def get_datetime_format(self, detected_format: str) -> List[str]:
        """
        Get datetime format strings based on detected format
        
        Args:
            detected_format: Detected format ('DMY', 'MDY', 'YMD')
            
        Returns:
            List of datetime format strings to try
        """
        if detected_format == 'DMY':
            return [
                '%d/%m/%Y %H:%M:%S',
                '%d/%m/%Y %H:%M',
                '%d-%m-%Y %H:%M:%S',
                '%d-%m-%Y %H:%M',
                '%d.%m.%Y %H:%M:%S',
                '%d.%m.%Y %H:%M',
                '%d/%m/%y %H:%M:%S',
                '%d/%m/%y %H:%M',
                '%d-%m-%y %H:%M:%S',
                '%d-%m-%y %H:%M',
            ]
        elif detected_format == 'MDY':
            return [
                '%m/%d/%Y %H:%M:%S',
                '%m/%d/%Y %H:%M',
                '%m-%d-%Y %H:%M:%S',
                '%m-%d-%Y %H:%M',
                '%m.%d.%Y %H:%M:%S',
                '%m.%d.%Y %H:%M',
                '%m/%d/%y %H:%M:%S',
                '%m/%d/%y %H:%M',
                '%m-%d-%y %H:%M:%S',
                '%m-%d-%y %H:%M',
            ]
        elif detected_format == 'YMD':
            return [
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d %H:%M',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y.%m.%d %H:%M:%S',
                '%Y.%m.%d %H:%M',
                '%y/%m/%d %H:%M:%S',
                '%y/%m/%d %H:%M',
                '%y-%m-%d %H:%M:%S',
                '%y-%m-%d %H:%M',
            ]
        else:
            # Return all formats as fallback
            return self.get_datetime_format('DMY') + self.get_datetime_format('MDY') + self.get_datetime_format('YMD')
    
    def get_date_format(self, detected_format: str) -> List[str]:
        """
        Get date-only format strings based on detected format
        
        Args:
            detected_format: Detected format ('DMY', 'MDY', 'YMD')
            
        Returns:
            List of date format strings to try
        """
        if detected_format == 'DMY':
            return [
                '%d/%m/%Y',
                '%d-%m-%Y',
                '%d.%m.%Y',
                '%d/%m/%y',
                '%d-%m-%y',
                '%d.%m.%y',
            ]
        elif detected_format == 'MDY':
            return [
                '%m/%d/%Y',
                '%m-%d-%Y',
                '%m.%d.%Y',
                '%m/%d/%y',
                '%m-%d-%y',
                '%m.%d.%y',
            ]
        elif detected_format == 'YMD':
            return [
                '%Y/%m/%d',
                '%Y-%m-%d',
                '%Y.%m.%d',
                '%y/%m/%d',
                '%y-%m-%d',
                '%y.%m.%d',
            ]
        else:
            # Return all formats as fallback
            return self.get_date_format('DMY') + self.get_date_format('MDY') + self.get_date_format('YMD')
