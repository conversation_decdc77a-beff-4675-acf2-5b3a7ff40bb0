#!/usr/bin/env python3
"""
Quick test to compare API vs local processing
"""

import os
import sys
import subprocess

def test_local_vs_api():
    """Test local main_s3.py vs API processing"""
    print("🔍 QUICK API vs LOCAL TEST")
    print("=" * 50)
    
    input_file = "USA AI Datacenter Demand (1).xlsx"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Test 1: Local main_s3.py (should work correctly)
    print(f"🔧 Testing local main_s3.py...")
    
    cmd = [
        "python3", "main_s3.py",
        "--file", input_file,
        "--output_file", "quick_local_test.csv",
        "--overwrite"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"   ✅ Local processing completed")
            
            if os.path.exists("quick_local_test.csv"):
                with open("quick_local_test.csv", 'r') as f:
                    lines = f.readlines()
                
                print(f"   📊 Local result: {len(lines)} lines")
                print(f"   📋 Sample data:")
                for i in range(1, min(4, len(lines))):
                    print(f"      {lines[i].strip()}")
                
                # Check if values match my_results6.csv
                if os.path.exists("my_results6.csv"):
                    with open("my_results6.csv", 'r') as f:
                        original_lines = f.readlines()
                    
                    if len(lines) == len(original_lines):
                        print(f"   ✅ Line count matches my_results6.csv")
                        
                        # Check first few values
                        matches = 0
                        for i in range(1, min(4, len(lines))):
                            if lines[i].strip() == original_lines[i].strip():
                                matches += 1
                        
                        if matches == 3:
                            print(f"   ✅ Values match my_results6.csv - LOCAL IS CORRECT")
                        else:
                            print(f"   ❌ Values differ from my_results6.csv")
                    else:
                        print(f"   ❌ Line count differs from my_results6.csv")
            else:
                print(f"   ❌ Output file not created")
                return False
        else:
            print(f"   ❌ Local processing failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"   ❌ Local processing timed out")
        return False
    
    print(f"\n🌐 API Issue Analysis:")
    print(f"   The API is calling a different code path than main_s3.py")
    print(f"   API imports: from main_s3 import process_s3_file")
    print(f"   But the preserve_data parameter was added to main_s3.py")
    print(f"   The API needs to use the SAME function as main_s3.py")
    
    return True

def show_api_fix_needed():
    """Show what needs to be fixed in the API"""
    print(f"\n🔧 API FIX REQUIRED:")
    print("=" * 50)
    
    print(f"❌ CURRENT API ISSUE:")
    print(f"   • API calls process_s3_file() without preserve_data parameter")
    print(f"   • This means ALL data modification functions are ACTIVE")
    print(f"   • Values get changed by outlier detection, scaling, rounding")
    print(f"   • Time formatting gets messed up")
    
    print(f"\n✅ SOLUTION:")
    print(f"   • API must call process_s3_file() WITH preserve_data=True")
    print(f"   • This disables all data modification functions")
    print(f"   • Values are preserved exactly as in input")
    print(f"   • Time progression works correctly")
    
    print(f"\n📝 CODE CHANGE NEEDED:")
    print(f"   In app/services.py line 142:")
    print(f"   OLD: result = process_s3_file(file_name, llm_handler, bucket_name)")
    print(f"   NEW: result = process_s3_file(file_name, llm_handler, bucket_name, preserve_data=True)")
    
    print(f"\n🎯 EXPECTED RESULT AFTER FIX:")
    print(f"   • API output should match my_results6.csv exactly")
    print(f"   • Values: 0.82, 0.81, 0.8, 0.79 (not 0.034374999999999996)")
    print(f"   • Time: 00:00, 01:00, 02:00, 03:00 (not all 00:00)")
    print(f"   • Processing time: Fast (like local)")

def main():
    """Run the quick test"""
    print("🚀 QUICK API DIAGNOSIS")
    print("=" * 70)
    
    success = test_local_vs_api()
    show_api_fix_needed()
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ DIAGNOSIS COMPLETE!")
        print("\n🎯 SUMMARY:")
        print("  • Local main_s3.py works correctly")
        print("  • API uses different code path with data modifications")
        print("  • Fix: API must call with preserve_data=True")
        print("  • After fix: API should match local output exactly")
    else:
        print("❌ DIAGNOSIS FAILED!")
        print("Check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
