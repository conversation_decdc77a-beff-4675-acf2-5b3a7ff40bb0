from fastapi import <PERSON><PERSON><PERSON>, Depends, Response
from fastapi.middleware.cors import CORSMiddleware
from .models import ProcessFileRequest, ProcessFileResponse, ProcessMetadataRequest, ProcessMetadataResponse
from .services import process_file_from_s3, process_metadata, ClientError, ServerError
from .config import API_TITLE, API_DESCRIPTION, API_VERSION
from .auth import verify_api_key

# Create FastAPI app
app = FastAPI(
    title=API_TITLE,
    description=f"{API_DESCRIPTION}\n\nAuthentication: This API requires an X-API-Key header for authentication.",
    version=API_VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint to check if the API is running"""
    return {"status_code": 200, "error": None, "message": "Demand Data Pipeline API is running"}

@app.post("/process-file", response_model=ProcessFileResponse)
async def process_file(request: ProcessFileRequest, response: Response, api_key: str = Depends(verify_api_key)):
    """
    Process a demand data file from S3

    This endpoint takes an S3 input file path and an output destination,
    processes the file, and saves the output with the same filename as the input.

    - **input_file**: S3 path to the input file (e.g., "path/to/input.csv")
    - **output_path**: S3 path where to save the output file (e.g., "path/to/output/")
    - **Note**: Data is always expanded to a full year

    The output file will be in CSV format with two columns:
    - Timestamp: Combined date and time (e.g., "01/01/25 18:00")
    - Demand: The demand value

    Returns:
    - **200**: Success with status_code=200, error=None, message=success message
    - **400**: Bad Request with status_code=400, error="Bad Request", message=error details
    - **500**: Internal Server Error with status_code=500, error="Internal Server Error", message=error details

    Authentication:
    - Requires a valid API key in the X-API-Key header
    """
    try:
        output_file = process_file_from_s3(request.input_file, request.output_path)
        response.status_code = 200
        return ProcessFileResponse(
            status_code=200,
            error=None,
            message=f"File processed and uploaded successfully to {output_file}"
        )
    except ClientError as e:
        response.status_code = 400
        return ProcessFileResponse(
            status_code=400,
            error="Bad Request",
            message=str(e)
        )
    except ServerError as e:
        response.status_code = 500
        return ProcessFileResponse(
            status_code=500,
            error="Internal Server Error",
            message=str(e)
        )
    except Exception as e:
        response.status_code = 500
        return ProcessFileResponse(
            status_code=500,
            error="Internal Server Error",
            message=f"An unexpected error occurred: {str(e)}"
        )

@app.post("/process-metadata", response_model=ProcessMetadataResponse)
async def process_metadata_endpoint(request: ProcessMetadataRequest, response: Response, api_key: str = Depends(verify_api_key)):
    """
    Process metadata for a demand data file and save it as JSON

    This endpoint takes an S3 input file path, an output destination, and metadata parameters,
    creates a JSON file with the metadata, and saves it with the same filename as the input but with a .json extension.

    - **input_file**: S3 path to the input file (e.g., "path/to/input.csv")
    - **output_path**: S3 path where to save the output file (e.g., "path/to/output/")
    - **country_code**: Country code (e.g., "IN" or "IND" for India) or country name (e.g., "India")
    - **industry_type**: Industry type (e.g., "Commercial")
    - **sub_industry_type**: Sub-industry type (e.g., "Data-center")

    The output file will be in JSON format containing:
    - timezone: Timezone determined from the country code
    - industry_type: The provided industry type
    - sub_industry_type: The provided sub-industry type

    Returns:
    - **200**: Success with status_code=200, error=None, message=success message
    - **400**: Bad Request with status_code=400, error="Bad Request", message=error details
    - **500**: Internal Server Error with status_code=500, error="Internal Server Error", message=error details

    Authentication:
    - Requires a valid API key in the X-API-Key header
    """
    try:
        output_file = process_metadata(
            request.input_file,
            request.output_path,
            request.country_code,
            request.industry_type,
            request.sub_industry_type
        )
        response.status_code = 200
        return ProcessMetadataResponse(
            status_code=200,
            error=None,
            message=f"Metadata processed and uploaded successfully to {output_file}"
        )
    except ClientError as e:
        response.status_code = 400
        return ProcessMetadataResponse(
            status_code=400,
            error="Bad Request",
            message=str(e)
        )
    except ServerError as e:
        response.status_code = 500
        return ProcessMetadataResponse(
            status_code=500,
            error="Internal Server Error",
            message=str(e)
        )
    except Exception as e:
        response.status_code = 500
        return ProcessMetadataResponse(
            status_code=500,
            error="Internal Server Error",
            message=f"An unexpected error occurred: {str(e)}"
        )
