import os
import logging
import sys
import json
from typing import Dict, Any, List, Optional
import pytz
import pycountry
from datetime import datetime
import botocore.exceptions

# Add the Demand Data directory to the path to import the existing modules
demand_data_path = os.path.abspath(os.path.join(os.path.expanduser("~"), "SE-CLEMAI-DemandData"))
sys.path.append(demand_data_path)
print(f"Added to path: {demand_data_path}")

# Import the necessary modules from the Demand Data Pipeline
from s3_file_handler import S3FileHandler
from main_s3 import process_s3_file
from llm_handler import LLMHandler
from data_processor import DataProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Custom exceptions
class ClientError(Exception):
    """Raised for client-side errors (400 Bad Request)"""
    pass

class ServerError(Exception):
    """Raised for server-side errors (500 Internal Server Error)"""
    pass

def process_s3_file_preserve_original(file_name: str, llm_handler: LLMHandler = None, bucket_name: str = None) -> List[Dict[str, Any]]:
    """
    Process a file from S3 while preserving original data points exactly as they are.
    This function temporarily disables outlier detection to prevent modification of real data.

    Args:
        file_name: Name of the file in S3 bucket
        llm_handler: LLMHandler instance for complex parsing tasks
        bucket_name: Optional bucket name to override the one in .env

    Returns:
        List of dictionaries with Date, Time, and Demand fields

    Raises:
        ClientError: For client-side issues (e.g., file not found, invalid format)
        ServerError: For server-side processing errors
    """
    logger.info(f"Processing S3 file with original data preservation: {file_name}")

    original_outlier_method = None
    try:
        # Store original methods for restoration
        original_outlier_method = DataProcessor.detect_and_handle_outliers
        original_magnitude_method = DataProcessor._detect_magnitude
        original_cumulative_method = DataProcessor.handle_cumulative_data
        original_aggregate_method = DataProcessor.aggregate_to_hourly

        def preserve_original_data(self, data):
            logger.warning("🚨 OUTLIER DETECTION DISABLED - preserving all original data points")
            logger.warning(f"🔍 Data points being preserved: {len(data) if hasattr(data, '__len__') else 'unknown'}")
            return data

        def preserve_original_magnitude(self, values):
            logger.warning("🚨 MAGNITUDE DETECTION DISABLED - no unit scaling applied")
            return 1.0

        def preserve_cumulative_data(self, data):
            logger.warning("🚨 CUMULATIVE DATA CONVERSION DISABLED - preserving original values")
            logger.warning(f"🔍 Original data values being preserved: {len(data) if hasattr(data, '__len__') else 'unknown'}")
            return data

        def preserve_aggregation(self, data):
            logger.warning("🚨 DATA AGGREGATION DISABLED - preserving original time resolution")
            logger.warning(f"🔍 Original time resolution being preserved: {len(data) if hasattr(data, '__len__') else 'unknown'}")
            return data

        # Disable ALL data modification methods
        DataProcessor.detect_and_handle_outliers = preserve_original_data
        DataProcessor._detect_magnitude = preserve_original_magnitude
        DataProcessor.handle_cumulative_data = preserve_cumulative_data
        DataProcessor.aggregate_to_hourly = preserve_aggregation

        # Set precision preservation flag
        DataProcessor.preserve_original_precision = True
        logger.warning("🚨 PRECISION PRESERVATION ENABLED - original decimal precision will be maintained")

        result = process_s3_file(file_name, llm_handler, bucket_name)

        if result and len(result) > 0:
            logger.info(f"Successfully processed file with {len(result)} data points")
            logger.info("🔍 SAMPLE OUTPUT VALUES (first 5):")
            for item in result[:5]:
                if isinstance(item, dict) and 'Demand' in item:
                    logger.info(f"   {item.get('Date', 'N/A')} {item.get('Time', 'N/A')} -> {item.get('Demand', 'N/A')}")
        else:
            logger.error("Processing returned empty or invalid result")
            raise ServerError("File processing completed but returned no valid data")

        logger.info("Successfully processed file with original data preserved")
        return result

    except botocore.exceptions.ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        if error_code == 'NoSuchKey':
            raise ClientError(f"File not found: {file_name}. Please check if the file path is correct and the file exists in the S3 bucket.")
        elif error_code == 'NoSuchBucket':
            raise ClientError(f"S3 bucket not found: {bucket_name}. Please check if the bucket name is correct and you have access to it.")
        elif error_code == 'AccessDenied':
            raise ClientError(f"Access denied to S3 bucket or file. Please check your AWS credentials and permissions.")
        else:
            raise ServerError(f"S3 client error during file processing: {str(e)}")
    except ValueError as e:
        error_msg = str(e)
        if "timeout" in error_msg.lower():
            raise ServerError(f"File processing timed out: {error_msg}")
        elif "format" in error_msg.lower():
            raise ClientError(f"File format issue: {error_msg}")
        elif "empty" in error_msg.lower() or "no data" in error_msg.lower():
            raise ClientError(f"File contains no valid data: {error_msg}")
        elif "corrupted" in error_msg.lower():
            raise ClientError(f"File appears to be corrupted: {error_msg}")
        else:
            raise ClientError(f"Invalid file format or data: {error_msg}")
    except TimeoutError as e:
        raise ServerError(f"File processing timed out: {str(e)}")
    except Exception as e:
        logger.error(f"Error in preserve_original processing: {str(e)}")
        error_msg = str(e)
        if "memory" in error_msg.lower():
            raise ServerError(f"File too large to process: {error_msg}")
        elif "permission" in error_msg.lower():
            raise ServerError(f"Permission error: {error_msg}")
        else:
            raise ServerError(f"Failed to process file: {error_msg}")
    finally:
        # Restore ALL original methods to ensure no side effects
        if original_outlier_method is not None:
            DataProcessor.detect_and_handle_outliers = original_outlier_method
            logger.debug("Restored original outlier detection method")
        if 'original_magnitude_method' in locals():
            DataProcessor._detect_magnitude = original_magnitude_method
            logger.debug("Restored original magnitude detection method")
        if 'original_cumulative_method' in locals():
            DataProcessor.handle_cumulative_data = original_cumulative_method
            logger.debug("Restored original cumulative data handling method")
        if 'original_aggregate_method' in locals():
            DataProcessor.aggregate_to_hourly = original_aggregate_method
            logger.debug("Restored original aggregation method")
        # Reset precision preservation flag
        DataProcessor.preserve_original_precision = False
        logger.debug("Reset precision preservation flag")

# Default timezone mapping for common countries
DEFAULT_COUNTRY_TIMEZONES = {
    "US": "America/New_York",
    "CA": "America/Toronto",
    "GB": "Europe/London",
    "DE": "Europe/Berlin",
    "FR": "Europe/Paris",
    "IT": "Europe/Rome",
    "ES": "Europe/Madrid",
    "JP": "Asia/Tokyo",
    "CN": "Asia/Shanghai",
    "IN": "Asia/Kolkata",
    "AU": "Australia/Sydney",
    "NZ": "Pacific/Auckland",
    "BR": "America/Sao_Paulo",
    "RU": "Europe/Moscow",
    "ZA": "Africa/Johannesburg",
}

# LLM configuration
LLM_CONFIG = {
    "api_base": "http://192.168.0.152:11435/v1",
    "api_key": "dummy-key",
    "model_name": "gemma3:12b",
    "temperature": 0
}

def process_file_from_s3(input_file: str, output_path: str) -> str:
    """
    Process a file from S3 and save the output to S3 as CSV

    Args:
        input_file: S3 path to the input file
        output_path: S3 path where to save the output file

    Returns:
        Output file path

    Raises:
        ClientError: For invalid inputs or client-side issues
        ServerError: For internal processing or upload failures
    """
    try:
        if input_file.startswith('s3://'):
            parts = input_file.replace('s3://', '').split('/', 1)
            if len(parts) > 1:
                bucket_name = parts[0]
                file_key = parts[1]
            else:
                raise ClientError(f"Invalid S3 URL format: {input_file}")
        else:
            file_key = input_file
            bucket_name = None

        # Validate file extension
        valid_extensions = ('.csv', '.xlsx', '.xls')
        if not any(file_key.lower().endswith(ext) for ext in valid_extensions):
            raise ClientError(f"Unsupported file format for {input_file}. Supported formats are CSV and Excel (.csv, .xlsx, .xls).")

        llm_handler = LLMHandler(LLM_CONFIG)
        logger.info(f"Processing file: {input_file}")
        processed_data = process_s3_file_preserve_original(file_key, llm_handler, bucket_name=bucket_name)

        if not processed_data:
            raise ClientError(f"Failed to process file: {input_file}. The file is empty or could not be parsed. Please check if the file contains valid data.")

        if "IsGenerated" in processed_data[0]:
            for item in processed_data:
                if "IsGenerated" in item:
                    del item["IsGenerated"]

        filename = os.path.basename(file_key)
        base_filename, _ = os.path.splitext(filename)
        output_filename = f"{base_filename}.csv"

        if output_path.startswith('s3://'):
            parts = output_path.replace('s3://', '').split('/', 1)
            if len(parts) > 1:
                output_bucket = parts[0]
                output_path_prefix = parts[1]
            else:
                output_bucket = parts[0]
                output_path_prefix = ""
            if output_path_prefix.endswith('/') or not output_path_prefix:
                output_file = f"{output_path_prefix}{output_filename}"
            else:
                output_file = f"{output_path_prefix}/{output_filename}"
        else:
            output_bucket = None
            if output_path.endswith('/'):
                output_file = f"{output_path}{output_filename}"
            else:
                output_file = f"{output_path}/{output_filename}"

        s3_handler = S3FileHandler()
        logger.info(f"Uploading data as CSV to {output_file}")
        success = s3_handler.upload_csv(processed_data, output_file, bucket_name=output_bucket)

        if output_bucket:
            full_output_path = f"s3://{output_bucket}/{output_file}"
        else:
            full_output_path = output_file

        if not success:
            raise ServerError(f"Failed to upload processed data to S3: {full_output_path}. Please check S3 permissions and bucket access.")

        return full_output_path

    except botocore.exceptions.ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        if error_code == 'NoSuchKey':
            raise ClientError(f"File not found: {input_file}. Please check if the file path is correct and the file exists in the S3 bucket.")
        elif error_code == 'NoSuchBucket':
            raise ClientError(f"S3 bucket not found. Please check if the bucket name is correct and you have access to it.")
        elif error_code == 'AccessDenied':
            raise ClientError(f"Access denied to S3 bucket or file. Please check your AWS credentials and permissions.")
        else:
            raise ServerError(f"S3 client error: {str(e)}")
    except ClientError:
        raise  # Re-raise ClientError to ensure it triggers a 400 response
    except Exception as e:
        if "openpyxl" in str(e):
            raise ServerError(f"Excel file processing error: Missing required dependency. Please contact the administrator.")
        raise ServerError(f"Error processing file: {str(e)}")

def process_metadata(input_file: str, output_path: str, country_code: str, industry_type: str, sub_industry_type: str) -> str:
    """
    Process metadata and save it as a JSON file to S3

    Args:
        input_file: S3 path to the input file
        output_path: S3 path where to save the output file
        country_code: Country code or name
        industry_type: Industry type
        sub_industry_type: Sub-industry type

    Returns:
        Output file path

    Raises:
        ClientError: For invalid inputs or client-side issues
        ServerError: For internal processing or upload failures
    """
    try:
        timezone = get_timezone_from_country(country_code)
        if not timezone:
            raise ClientError(f"Invalid or unsupported country code/name: {country_code}")

        metadata = {
            "timezone": timezone,
            "industry_type": industry_type,
            "sub_industry_type": sub_industry_type
        }

        if input_file.startswith('s3://'):
            parts = input_file.replace('s3://', '').split('/', 1)
            if len(parts) > 1:
                file_key = parts[1]
            else:
                raise ClientError(f"Invalid S3 URL format: {input_file}")
        else:
            file_key = input_file

        filename = os.path.basename(file_key)
        base_filename, _ = os.path.splitext(filename)
        output_filename = f"{base_filename}.json"

        if output_path.startswith('s3://'):
            parts = output_path.replace('s3://', '').split('/', 1)
            if len(parts) > 1:
                output_bucket = parts[0]
                output_path_prefix = parts[1]
            else:
                output_bucket = parts[0]
                output_path_prefix = ""
            if output_path_prefix.endswith('/') or not output_path_prefix:
                output_file = f"{output_path_prefix}{output_filename}"
            else:
                output_file = f"{output_path_prefix}/{output_filename}"
        else:
            output_bucket = None
            if output_path.endswith('/'):
                output_file = f"{output_path}{output_filename}"
            else:
                output_file = f"{output_path}/{output_filename}"

        s3_handler = S3FileHandler()
        metadata_json = json.dumps(metadata, indent=2)
        logger.info(f"Uploading metadata as JSON to {output_file}")
        success = s3_handler.upload_file_content(metadata_json, output_file, bucket_name=output_bucket)

        if output_bucket:
            full_output_path = f"s3://{output_bucket}/{output_file}"
        else:
            full_output_path = output_file

        if not success:
            raise ServerError(f"Failed to upload metadata to S3: {full_output_path}. Please check S3 permissions and bucket access.")

        return full_output_path

    except botocore.exceptions.ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        if error_code == 'NoSuchBucket':
            raise ClientError(f"S3 bucket not found. Please check if the bucket name is correct and you have access to it.")
        elif error_code == 'AccessDenied':
            raise ClientError(f"Access denied to S3 bucket. Please check your AWS credentials and permissions.")
        else:
            raise ServerError(f"S3 client error: {str(e)}")
    except Exception as e:
        raise ServerError(f"Error processing metadata: {str(e)}")

def get_alpha2_code(country_identifier: str) -> Optional[str]:
    """
    Convert a country identifier (name, 2-letter code, or 3-letter code) to a 2-letter country code
    """
    if not country_identifier:
        return None

    if len(country_identifier) == 2:
        return country_identifier.upper()

    if len(country_identifier) == 3:
        try:
            country = pycountry.countries.get(alpha_3=country_identifier.upper())
            if country:
                return country.alpha_2
        except Exception as e:
            logger.error(f"Error converting 3-letter code {country_identifier}: {str(e)}")
            return None

    try:
        country = pycountry.countries.get(name=country_identifier)
        if country:
            return country.alpha_2

        for country in pycountry.countries:
            if country.name.lower() == country_identifier.lower():
                return country.alpha_2

        for country in pycountry.countries:
            if country_identifier.lower() in country.name.lower():
                logger.info(f"Fuzzy matched '{country_identifier}' to '{country.name}'")
                return country.alpha_2
    except Exception as e:
        logger.error(f"Error finding country by name {country_identifier}: {str(e)}")

    return None

def get_timezone_from_country(country_code: str) -> Optional[str]:
    """
    Get the timezone for a given country code (2-letter or 3-letter) or name
    """
    if not country_code:
        logger.warning("No country code provided")
        return None

    alpha2_code = get_alpha2_code(country_code)
    if not alpha2_code:
        logger.warning(f"Could not convert '{country_code}' to a valid 2-letter country code")
        return None

    timezone_id = DEFAULT_COUNTRY_TIMEZONES.get(alpha2_code)
    if not timezone_id:
        logger.warning(f"No timezone found for country code: {alpha2_code}")
        return None

    try:
        tz = pytz.timezone(timezone_id)
        now = datetime.now(tz)
        timezone_abbr = now.strftime('%Z')
        return timezone_abbr
    except Exception as e:
        logger.error(f"Error getting timezone for {alpha2_code}: {str(e)}")
        return None
