from pydantic import BaseModel, Field
from typing import Optional

class ProcessFileRequest(BaseModel):
    """
    Request model for processing a file from S3
    """
    input_file: str = Field(..., description="S3 path to the input file")
    output_path: str = Field(..., description="S3 path where to save the output file")

class ProcessFileResponse(BaseModel):
    """
    Response model for the process file endpoint
    """
    status_code: int = Field(..., description="HTTP status code (200, 400, or 500)")
    error: Optional[str] = Field(None, description="Short error description if the operation failed")
    message: str = Field(..., description="Detailed message describing the result")

class ProcessMetadataRequest(BaseModel):
    """
    Request model for processing metadata and saving it as a JSON file
    """
    input_file: str = Field(..., description="S3 path to the input file")
    output_path: str = Field(..., description="S3 path where to save the output file")
    country_code: str = Field(..., description="Country code (e.g., 'IN' for India)")
    industry_type: str = Field(..., description="Industry type (e.g., 'Commercial')")
    sub_industry_type: str = Field(..., description="Sub-industry type (e.g., 'Data-center')")

class ProcessMetadataResponse(BaseModel):
    """
    Response model for the process metadata endpoint
    """
    status_code: int = Field(..., description="HTTP status code (200, 400, or 500)")
    error: Optional[str] = Field(None, description="Short error description if the operation failed")
    message: str = Field(..., description="Detailed message describing the result")
