import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# AWS Configuration
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
REGION_NAME = os.getenv("REGION_NAME", "us-west-2")
INPUT_BUCKET = os.getenv("BUCKET_NAME", "demandprofiles")
OUTPUT_BUCKET = os.getenv("OUTPUT_BUCKET", "demandjsons")

# API Configuration
API_TITLE = "Demand Data Pipeline API"
API_DESCRIPTION = "API for processing demand data files from S3"
API_VERSION = "1.0.0"

# API Authentication
API_KEY = os.getenv("API_KEY", "your-default-api-key")  # Default key for development
if not API_KEY or API_KEY == "your-default-api-key":
    print("WARNING: Using default API key. Set the API_KEY environment variable for production.")
