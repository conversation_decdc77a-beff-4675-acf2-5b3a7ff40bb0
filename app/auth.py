from fastapi import Depends, HTTPException, Header
from .config import API_KEY

async def verify_api_key(x_api_key: str = Header(..., description="API Key for authentication")):
    """
    Verify that the X-API-Key header contains a valid API key.
    This function is used as a dependency for protected endpoints.
    
    Args:
        x_api_key: The API key from the X-API-Key header
        
    Returns:
        The API key if valid
        
    Raises:
        HTTPException: If the API key is invalid
    """
    if x_api_key != API_KEY:
        raise HTTPException(
            status_code=401,
            detail="Invalid API Key"
        )
    return x_api_key
