import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "app"))

# Import the necessary modules
from services import process_file_from_s3

def test_process_file():
    """
    Test the process_file_from_s3 function directly
    """
    # Get input file and output path from command line arguments or use defaults
    if len(sys.argv) > 2:
        input_file = sys.argv[1]
        output_path = sys.argv[2]
    else:
        # Default test values
        input_file = "format8_unix.csv"  # Just the file key, not the full S3 URL
        output_path = "output/"  # Output path in the output bucket

    print(f"Testing with input_file: {input_file}, output_path: {output_path}")

    # Call the function
    success, message, output_file, generated_points = process_file_from_s3(
        input_file,
        output_path
    )

    # Print the results
    print(f"Success: {success}")
    print(f"Message: {message}")
    print(f"Output file: {output_file}")
    print(f"Generated points: {generated_points}")

    return success

if __name__ == "__main__":
    test_process_file()
