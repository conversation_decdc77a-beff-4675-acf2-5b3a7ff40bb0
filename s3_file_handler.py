import boto3
import io
import os
import pandas as pd
import json
import logging
from typing import Tuple, Any
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class S3FileHandler:
    """
    Handles reading files from S3 buckets
    """

    def __init__(self, bucket_name=None):
        """
        Initialize the S3FileHandler using environment variables

        Args:
            bucket_name: Optional bucket name to override the one in .env
        """
        # Load S3 configuration from environment variables
        self.bucket_name = bucket_name or os.getenv('BUCKET_NAME')
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        region = os.getenv('REGION_NAME', 'us-east-1')
        self.output_bucket = os.getenv('OUTPUT_BUCKET')

        # Validate required environment variables
        if not self.bucket_name or not aws_access_key or not aws_secret_key:
            logger.warning("Missing required S3 environment variables. Check your .env file.")

        logger.info(f"Using S3 bucket: {self.bucket_name}")

        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key,
            region_name=region
        )

    def read_file(self, file_name: str) -> Tuple[Any, str]:
        """
        Read a file from S3 bucket

        Args:
            file_name: Name of the file in S3 bucket (without path)

        Returns:
            Tuple of (file_content, file_format)
        """
        try:
            # Get file extension
            _, ext = os.path.splitext(file_name)
            ext = ext.lower()

            # Get the file from S3
            logger.info(f"Reading file {file_name} from S3 bucket {self.bucket_name}")
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=file_name)
            file_content = response['Body'].read()

            if ext == '.csv':
                # Read CSV into pandas DataFrame
                df = pd.read_csv(io.BytesIO(file_content))
                logger.info(f"Successfully read CSV file from S3: {file_name}")
                return df, 'csv'

            elif ext == '.json':
                # Parse JSON
                data = json.loads(file_content.decode('utf-8'))
                logger.info(f"Successfully read JSON file from S3: {file_name}")
                return data, 'json'

            elif ext == '.txt':
                # Read text content
                content = file_content.decode('utf-8')
                logger.info(f"Successfully read TXT file from S3: {file_name}")
                return content, 'txt'

            elif ext == '.xlsx':
                # Read Excel into pandas DataFrame
                try:
                    df = pd.read_excel(io.BytesIO(file_content), sheet_name=0)
                    logger.info(f"Successfully read Excel file from S3: {file_name}")
                    return df, 'excel'
                except Exception as e:
                    logger.error(f"Error reading Excel file from S3: {str(e)}")
                    return None, None

            else:
                logger.warning(f"Unsupported file extension: {ext}")
                return None, None

        except Exception as e:
            logger.error(f"Error reading file {file_name} from S3: {str(e)}")
            return None, None

    def list_files(self, prefix: str = '') -> list:
        """
        List files in the S3 bucket

        Args:
            prefix: Prefix to filter files (optional)

        Returns:
            List of file keys
        """
        try:
            response = self.s3_client.list_objects_v2(Bucket=self.bucket_name, Prefix=prefix)

            if 'Contents' in response:
                return [item['Key'] for item in response['Contents']]
            else:
                return []

        except Exception as e:
            logger.error(f"Error listing files in S3 bucket: {str(e)}")
            return []

    def upload_json(self, data: dict, file_name: str, bucket_name: str = None) -> bool:
        """
        Upload JSON data to the output S3 bucket

        Args:
            data: Dictionary data to upload as JSON
            file_name: Name of the file to create in S3
            bucket_name: Optional bucket name to override the output bucket in .env

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use specified bucket or default to output bucket
            target_bucket = bucket_name or self.output_bucket

            if not target_bucket:
                logger.error("No target bucket specified for upload")
                return False

            # Convert dictionary to JSON string
            json_data = json.dumps(data, indent=2)

            # Upload to S3
            self.s3_client.put_object(
                Bucket=target_bucket,
                Key=file_name,
                Body=json_data,
                ContentType='application/json'
            )

            logger.info(f"Successfully uploaded {file_name} to S3 bucket {target_bucket}")
            return True

        except Exception as e:
            logger.error(f"Error uploading JSON to S3: {str(e)}")
            return False

    def upload_csv(self, data: list, file_name: str, bucket_name: str = None) -> bool:
        """
        Upload data as CSV to the output S3 bucket with Timestamp and Demand columns

        Args:
            data: List of dictionaries with Date, Time, and Demand fields
            file_name: Name of the file to create in S3
            bucket_name: Optional bucket name to override the output bucket in .env

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use specified bucket or default to output bucket
            target_bucket = bucket_name or self.output_bucket

            if not target_bucket:
                logger.error("No target bucket specified for upload")
                return False

            # Convert data to DataFrame with Timestamp and Demand columns
            rows = []
            for item in data:
                # Combine Date and Time into a single Timestamp column
                timestamp = f"{item['Date']} {item['Time']}"
                demand = item['Demand']
                rows.append({"Timestamp": timestamp, "Demand": demand})

            df = pd.DataFrame(rows)

            # Convert DataFrame to CSV
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)

            # Upload to S3
            self.s3_client.put_object(
                Bucket=target_bucket,
                Key=file_name,
                Body=csv_buffer.getvalue(),
                ContentType='text/csv'
            )

            logger.info(f"Successfully uploaded CSV {file_name} to S3 bucket {target_bucket}")
            return True

        except Exception as e:
            logger.error(f"Error uploading CSV to S3: {str(e)}")
            return False

    def upload_file_content(self, content: str, file_name: str, bucket_name: str = None, content_type: str = None) -> bool:
        """
        Upload string content to the output S3 bucket

        Args:
            content: String content to upload
            file_name: Name of the file to create in S3
            bucket_name: Optional bucket name to override the output bucket in .env
            content_type: Optional content type (MIME type) for the file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use specified bucket or default to output bucket
            target_bucket = bucket_name or self.output_bucket

            if not target_bucket:
                logger.error("No target bucket specified for upload")
                return False

            # Determine content type based on file extension if not provided
            if content_type is None:
                _, ext = os.path.splitext(file_name)
                ext = ext.lower()

                if ext == '.json':
                    content_type = 'application/json'
                elif ext == '.csv':
                    content_type = 'text/csv'
                elif ext == '.txt':
                    content_type = 'text/plain'
                elif ext == '.html':
                    content_type = 'text/html'
                elif ext == '.xml':
                    content_type = 'application/xml'
                else:
                    content_type = 'application/octet-stream'

            # Upload to S3
            self.s3_client.put_object(
                Bucket=target_bucket,
                Key=file_name,
                Body=content,
                ContentType=content_type
            )

            logger.info(f"Successfully uploaded content to {file_name} in S3 bucket {target_bucket}")
            return True

        except Exception as e:
            logger.error(f"Error uploading content to S3: {str(e)}")
            return False
