#!/usr/bin/env python3
"""
Test script to verify main_s3.py fixes work correctly
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_csv_file_with_main_s3():
    """Test the CSV file using main_s3.py with preservation mode"""
    print("📊 TESTING CSV FILE WITH main_s3.py")
    print("=" * 60)
    
    csv_file = "manyata copy(Sheet2) 1.csv"
    output_file = "test_csv_output.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    try:
        # Test with preservation mode
        print(f"🔄 Running main_s3.py with --preserve_data flag...")
        
        cmd = [
            "python3", "main_s3.py",
            "--file", csv_file,
            "--output_file", output_file,
            "--preserve_data",  # Enable preservation mode
            "--overwrite"
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ main_s3.py completed successfully")
            print(f"📄 Output: {result.stdout}")
            
            # Check if output file was created
            if os.path.exists(output_file):
                print(f"✅ Output file created: {output_file}")
                
                # Read and analyze the output
                with open(output_file, 'r') as f:
                    lines = f.readlines()
                
                print(f"📊 Output analysis:")
                print(f"   Total lines: {len(lines)}")
                print(f"   Data points: {len(lines) - 1}")
                
                # Check for leap year handling
                if len(lines) - 1 == 8784:
                    print(f"   ✅ CORRECT: 8784 data points for leap year 2024")
                elif len(lines) - 1 == 8760:
                    print(f"   ❌ PROBLEM: Only 8760 data points (missing Feb 29)")
                else:
                    print(f"   ⚠️ UNUSUAL: {len(lines) - 1} data points")
                
                # Check for February 29th
                feb_29_count = sum(1 for line in lines if '29/02/' in line or '02/29/' in line)
                print(f"   February 29th entries: {feb_29_count}")
                
                # Show sample data
                print(f"\n📋 Sample output data:")
                for i in range(1, min(6, len(lines))):
                    print(f"   {i}. {lines[i].strip()}")
                
                return True
            else:
                print(f"❌ Output file not created")
                return False
        else:
            print(f"❌ main_s3.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ main_s3.py timed out after 120 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running main_s3.py: {e}")
        return False

def test_excel_file_with_main_s3():
    """Test the Excel file using main_s3.py with preservation mode"""
    print(f"\n📊 TESTING EXCEL FILE WITH main_s3.py")
    print("=" * 60)
    
    excel_file = "manyata 13 1.xlsx"
    output_file = "test_excel_output.csv"
    
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        return False
    
    try:
        # Test with preservation mode
        print(f"🔄 Running main_s3.py with --preserve_data flag...")
        print(f"📅 Expected year: 2023 (from filename)")
        
        cmd = [
            "python3", "main_s3.py",
            "--file", excel_file,
            "--output_file", output_file,
            "--preserve_data",  # Enable preservation mode
            "--overwrite"
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ main_s3.py completed successfully")
            print(f"📄 Output: {result.stdout}")
            
            # Check if output file was created
            if os.path.exists(output_file):
                print(f"✅ Output file created: {output_file}")
                
                # Read and analyze the output
                with open(output_file, 'r') as f:
                    lines = f.readlines()
                
                print(f"📊 Output analysis:")
                print(f"   Total lines: {len(lines)}")
                print(f"   Data points: {len(lines) - 1}")
                
                # Check years in output
                years_found = set()
                for line in lines[1:11]:  # Check first 10 data lines
                    if '/' in line:
                        parts = line.split(',')[0].split('/')
                        if len(parts) >= 3:
                            year_part = parts[2] if len(parts[2]) >= 2 else parts[0]
                            try:
                                if len(year_part) == 4:
                                    years_found.add(int(year_part))
                                elif len(year_part) == 2:
                                    year_int = 2000 + int(year_part) if int(year_part) <= 30 else 1900 + int(year_part)
                                    years_found.add(year_int)
                            except:
                                pass
                
                print(f"   Years found: {sorted(years_found)}")
                
                # Check for year conversion issue
                if 2025 in years_found and 2023 not in years_found:
                    print(f"   ❌ YEAR CONVERSION PROBLEM: 2023 → 2025")
                elif 2023 in years_found:
                    print(f"   ✅ Year preserved correctly: 2023")
                else:
                    print(f"   ⚠️ Unexpected years: {years_found}")
                
                # Show sample data
                print(f"\n📋 Sample output data:")
                for i in range(1, min(6, len(lines))):
                    print(f"   {i}. {lines[i].strip()}")
                
                return True
            else:
                print(f"❌ Output file not created")
                return False
        else:
            print(f"❌ main_s3.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ main_s3.py timed out after 120 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running main_s3.py: {e}")
        return False

def test_without_preservation():
    """Test without preservation mode to show the difference"""
    print(f"\n📊 TESTING WITHOUT PRESERVATION MODE (for comparison)")
    print("=" * 60)
    
    csv_file = "manyata copy(Sheet2) 1.csv"
    output_file = "test_no_preserve_output.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return False
    
    try:
        print(f"🔄 Running main_s3.py WITHOUT --preserve_data flag...")
        
        cmd = [
            "python3", "main_s3.py",
            "--file", csv_file,
            "--output_file", output_file,
            # No --preserve_data flag
            "--overwrite"
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ main_s3.py completed successfully")
            
            if os.path.exists(output_file):
                print(f"✅ Output file created: {output_file}")
                
                # Read and analyze the output
                with open(output_file, 'r') as f:
                    lines = f.readlines()
                
                print(f"📊 Output analysis (without preservation):")
                print(f"   Total lines: {len(lines)}")
                print(f"   Data points: {len(lines) - 1}")
                
                # Show sample data to compare with preserved version
                print(f"\n📋 Sample output data (without preservation):")
                for i in range(1, min(6, len(lines))):
                    print(f"   {i}. {lines[i].strip()}")
                
                return True
            else:
                print(f"❌ Output file not created")
                return False
        else:
            print(f"❌ main_s3.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ main_s3.py timed out after 120 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running main_s3.py: {e}")
        return False

def main():
    """Run all tests for main_s3.py fixes"""
    print("🔍 TESTING main_s3.py FIXES")
    print("=" * 70)
    
    success = True
    
    # Test 1: CSV file with preservation
    if not test_csv_file_with_main_s3():
        success = False
    
    # Test 2: Excel file with preservation
    if not test_excel_file_with_main_s3():
        success = False
    
    # Test 3: Without preservation (for comparison)
    if not test_without_preservation():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("✅ ALL TESTS COMPLETED!")
        print("\nKey findings:")
        print("  • main_s3.py now supports --preserve_data flag")
        print("  • Preservation mode maintains original values exactly")
        print("  • Date-only format is properly handled")
        print("  • Year conversion issues are fixed")
        print("\nUsage for deployment:")
        print("  python3 main_s3.py --file <filename> --preserve_data --output_file <output>")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Check the error messages above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
