import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, time, timedelta
import logging
import calendar

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Processes and transforms the data (scaling, aggregation)
    """

    def __init__(self):
        """
        Initialize the DataProcessor
        """
        # No parameters needed anymore since we're not scaling

    def _detect_magnitude(self, values: List[float]) -> float:
        """
        Detect the magnitude of values to determine appropriate scaling

        Args:
            values: List of numeric values

        Returns:
            Detected magnitude (1.0 for values around 1-100, 0.001 for values around 1000-100000, etc.)
        """
        if not values:
            return 1.0

        # Get the median value to avoid outliers
        median_value = sorted(values)[len(values) // 2]
        abs_median = abs(median_value)

        if abs_median == 0:
            return 1.0
        elif abs_median < 0.01:  # Very small values (< 0.01)
            return 100.0
        elif abs_median < 1.0:  # Small values (0.01 - 1.0)
            return 10.0
        elif abs_median < 10.0:  # Values around 1-10
            return 1.0
        elif abs_median < 100.0:  # Values around 10-100
            return 1.0
        elif abs_median < 1000.0:  # Values around 100-1000
            return 0.1
        elif abs_median < 10000.0:  # Values around 1000-10000
            return 0.01
        elif abs_median < 100000.0:  # Values around 10000-100000
            return 0.001
        else:  # Very large values
            return 0.0001

    def detect_and_handle_outliers(self, data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
        """
        Detect and handle outliers in demand data using Modified Z-Score with contextual awareness,
        persistence validation, and pattern-based replacement.

        Args:
            data: List of (timestamp, demand) tuples

        Returns:
            List of (timestamp, demand) tuples with outliers replaced
        """
        if not data:
            return []

        # Convert to DataFrame for easier processing
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])

        # Add time features for contextual analysis
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['date'] = df['timestamp'].dt.date

        # Initialize outlier column
        df['is_outlier'] = False

        # 1. DETECTION: Modified Z-Score with contextual awareness
        # Group data by hour of day and day of week
        for (hour, day_of_week), group in df.groupby(['hour', 'day_of_week']):
            # Skip if group is too small
            if len(group) < 3:
                continue

            # Calculate median and MAD for this specific context
            median = group['demand'].median()
            mad = np.median(np.abs(group['demand'] - median))

            # Avoid division by zero
            if mad == 0:
                mad = np.mean(np.abs(group['demand'] - median)) or 1e-10

            # Calculate modified Z-score
            modified_z = 0.6745 * abs(group['demand'] - median) / mad

            # Mark as outlier if modified Z-score exceeds threshold
            # Using a conservative threshold of 3.5
            outlier_indices = group.index[modified_z > 3.5]
            df.loc[outlier_indices, 'is_outlier'] = True

        # Count initially detected outliers
        initial_outlier_count = df['is_outlier'].sum()
        if initial_outlier_count > 0:
            logger.info(f"Initially detected {initial_outlier_count} outliers using Modified Z-Score method")

            # 2. VALIDATION: Persistence and Rate of Change Checks
            # Store initial outliers for validation
            initial_outliers = df['is_outlier'].copy()

            # Check for persistence (isolated outliers)
            for idx in df[initial_outliers].index:
                # Check if neighboring points are also outliers
                neighbors_outlier = False
                if idx > 0 and idx < len(df) - 1:
                    neighbors_outlier = df.loc[idx-1, 'is_outlier'] or df.loc[idx+1, 'is_outlier']

                # If this is an isolated outlier, validate with rate of change check
                if not neighbors_outlier:
                    if idx > 0 and idx < len(df) - 1:
                        # Calculate rates of change before and after
                        rate_before = abs(df.loc[idx, 'demand'] - df.loc[idx-1, 'demand'])
                        rate_after = abs(df.loc[idx+1, 'demand'] - df.loc[idx, 'demand'])

                        # Calculate typical rate of change (standard deviation of differences)
                        typical_rate = df['demand'].diff().abs().std()

                        # If both rates are extreme, confirm as outlier
                        # Otherwise, it might be a legitimate spike/drop
                        if rate_before > 3 * typical_rate and rate_after > 3 * typical_rate:
                            df.loc[idx, 'is_outlier'] = True
                        else:
                            df.loc[idx, 'is_outlier'] = False

            # Count validated outliers
            validated_outlier_count = df['is_outlier'].sum()
            logger.info(f"After validation, {validated_outlier_count} outliers remain")

            # 3. REPLACEMENT: Pattern-Based Replacement
            if validated_outlier_count > 0:
                # Create a copy of demand values for replacement
                df['demand_cleaned'] = df['demand'].copy()

                # Replace confirmed outliers
                for idx in df[df['is_outlier']].index:
                    hour = df.loc[idx, 'hour']
                    day_of_week = df.loc[idx, 'day_of_week']

                    # Find similar time periods (same hour and day of week) that are not outliers
                    similar_periods = df[(df['hour'] == hour) &
                                        (df['day_of_week'] == day_of_week) &
                                        (~df['is_outlier'])]

                    if len(similar_periods) >= 3:  # Require at least 3 similar periods for reliable pattern
                        # Replace with median of similar periods
                        df.loc[idx, 'demand_cleaned'] = similar_periods['demand'].median()
                        logger.debug(f"Replaced outlier at {df.loc[idx, 'timestamp']} using pattern-based replacement")
                    else:
                        # If not enough similar periods, mark for interpolation
                        df.loc[idx, 'demand_cleaned'] = np.nan
                        logger.debug(f"Marked outlier at {df.loc[idx, 'timestamp']} for interpolation")

                # Fill any remaining NaN values with interpolation
                df['demand_cleaned'] = df['demand_cleaned'].interpolate(method='linear')

                # Use the cleaned demand values
                return [(row['timestamp'], row['demand_cleaned']) for _, row in df.iterrows()]

        # If no outliers were detected or validated, return the original data
        return data

    def _convert_to_mw(self, value: float, unit: str) -> float:
        """
        Convert a value to MW based on its unit

        Args:
            value: The value to convert
            unit: The unit of the value

        Returns:
            Value in MW
        """
        unit = unit.lower()

        if unit == 'mw':
            return value
        elif unit == 'kw':
            return value / 1000.0
        elif unit == 'w':
            return value / 1000000.0
        elif unit == 'gw':
            return value * 1000.0
        else:  # Unknown unit, try to detect based on magnitude
            magnitude = self._detect_magnitude([value])
            return value * magnitude

    # The scale_to_target_range method has been removed as we're now only converting to MW without scaling

    def aggregate_to_hourly(self, data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
        """
        Aggregate sub-hourly data to hourly values

        Args:
            data: List of (timestamp, demand) tuples

        Returns:
            List of (timestamp, aggregated_demand) tuples with hourly resolution
        """
        if not data:
            return []

        # Create a DataFrame for easier aggregation
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])

        # Round timestamps to the nearest hour
        df['hour'] = df['timestamp'].apply(lambda x: x.replace(minute=0, second=0, microsecond=0))

        # Aggregate by hour (using mean)
        hourly_data = df.groupby('hour')['demand'].mean().reset_index()

        return [(row['hour'], row['demand']) for _, row in hourly_data.iterrows()]

    def fill_missing_days(self, data: List[Tuple[datetime, float]],
                      start_date: datetime = None,
                      end_date: datetime = None,
                      num_similar_days: int = 7) -> List[Tuple[datetime, float]]:
        """
        Fill in missing days with weighted average of closest days,
        with special handling for days missing at the start or end of the period.

        Args:
            data: List of (timestamp, demand) tuples
            start_date: Start date of the period to fill (if None, use min date in data)
            end_date: End date of the period to fill (if None, use max date in data)
            num_similar_days: Number of similar days to use for weighted average (default: 7)

        Returns:
            List of (timestamp, demand) tuples with no missing days
        """
        if not data:
            return []

        # Convert to DataFrame for easier processing
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])

        # Extract date and hour components
        df['date'] = df['timestamp'].dt.date
        df['hour'] = df['timestamp'].dt.hour

        # Determine start and end dates if not provided
        if start_date is None:
            start_date = df['timestamp'].min().date()
        if end_date is None:
            end_date = df['timestamp'].max().date()

        # Create complete date range
        all_dates = pd.date_range(start=start_date, end=end_date).date

        # Get unique dates in the data
        existing_dates = set(df['date'].unique())

        # Find missing dates - include all dates in the specified range
        missing_dates = [date for date in all_dates if date not in existing_dates]

        if not missing_dates:
            logger.info("No missing days detected")
            return data

        logger.info(f"Detected {len(missing_dates)} missing days. Filling using weighted average of {num_similar_days} similar days")

        # Get min and max dates in the existing data
        if existing_dates:
            min_existing_date = min(existing_dates)
            max_existing_date = max(existing_dates)
        else:
            return data  # No existing data to base filling on

        # Log the missing dates for debugging
        logger.info(f"Missing dates: {missing_dates}")
        logger.info(f"Existing date range: {min_existing_date} to {max_existing_date}")

        # For each missing date
        for missing_date in missing_dates:
            # Check if this is a boundary case
            is_before_data = missing_date < min_existing_date
            is_after_data = missing_date > max_existing_date

            # Get day of week for missing date (0=Monday, 6=Sunday)
            missing_day_of_week = missing_date.weekday()

            if is_before_data:
                # Missing day is before any existing data
                # Use the earliest available days
                template_dates = sorted(list(existing_dates))[:num_similar_days]

                # Calculate weights - give higher weight to earlier days and same day of week
                weights = []
                for i, template_date in enumerate(template_dates):
                    # Base weight is inverse of position (earlier days get higher weight)
                    position_weight = 1 / (i + 1)

                    # Additional weight if same day of week
                    day_of_week_match = 2.0 if template_date.weekday() == missing_day_of_week else 1.0

                    weights.append(position_weight * day_of_week_match)

            elif is_after_data:
                # Missing day is after all existing data
                # Use the latest available days
                template_dates = sorted(list(existing_dates), reverse=True)[:num_similar_days]

                # Calculate weights - give higher weight to later days and same day of week
                weights = []
                for i, template_date in enumerate(template_dates):
                    # Base weight is inverse of position (later days get higher weight)
                    position_weight = 1 / (i + 1)

                    # Additional weight if same day of week
                    day_of_week_match = 2.0 if template_date.weekday() == missing_day_of_week else 1.0

                    weights.append(position_weight * day_of_week_match)

            else:
                # Missing day is between existing data points
                # Calculate distance to all existing dates
                date_distances = [(abs((missing_date - existing_date).days), existing_date)
                                 for existing_date in existing_dates]

                # Sort by distance and take the closest N days
                closest_days = sorted(date_distances)[:num_similar_days]

                # Extract just the dates from the (distance, date) tuples
                template_dates = [date for _, date in closest_days]

                # Calculate weights based on inverse distance and day of week
                weights = []
                for distance, template_date in closest_days:
                    # Base weight is inverse distance (closer days get higher weight)
                    distance_weight = 1 / max(1, distance)

                    # Additional weight if same day of week
                    day_of_week_match = 2.0 if template_date.weekday() == missing_day_of_week else 1.0

                    weights.append(distance_weight * day_of_week_match)

            # Normalize weights
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]

            # Get hourly profiles for each of the template days
            hourly_profiles = []
            for template_date in template_dates:
                # Get the demand values for each hour of the template day
                day_profile = df[df['date'] == template_date].sort_values('hour')

                # Only use days with complete data (24 hours)
                if len(day_profile) == 24:
                    hourly_profiles.append((template_date, day_profile['demand'].values))
                else:
                    # For incomplete days, we'll adjust the weights later
                    hourly_profiles.append((template_date, None))

            # Adjust weights to account for incomplete days
            valid_indices = [i for i, (_, profile) in enumerate(hourly_profiles) if profile is not None]
            if not valid_indices:
                # No valid profiles found, skip this missing date
                logger.warning(f"No valid template days found for {missing_date}. Skipping.")
                continue

            # Recalculate weights considering only valid profiles
            valid_weights = [weights[i] for i in valid_indices]
            total_valid_weight = sum(valid_weights)
            normalized_valid_weights = [w / total_valid_weight for w in valid_weights]

            # Calculate weighted average profile
            weighted_profile = np.zeros(24)  # Assuming 24 hours in a day
            for idx, weight in zip(valid_indices, normalized_valid_weights):
                _, profile = hourly_profiles[idx]
                weighted_profile += weight * profile

            # Create new rows for the missing day
            new_rows = []
            for hour in range(24):
                timestamp = datetime.combine(missing_date, time(hour=hour))
                new_rows.append({'timestamp': timestamp, 'demand': weighted_profile[hour]})

            # Add the new rows to the DataFrame
            df = pd.concat([df, pd.DataFrame(new_rows)], ignore_index=True)

        # Sort by timestamp
        df = df.sort_values('timestamp')

        # Return as list of tuples
        return [(row['timestamp'], row['demand']) for _, row in df.iterrows()]

    def fill_missing_hours(self, data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
        """
        Fill in missing hours with interpolated values

        Args:
            data: List of (timestamp, demand) tuples

        Returns:
            List of (timestamp, demand) tuples with no missing hours
        """
        if not data:
            return []

        # Sort by timestamp
        data.sort(key=lambda x: x[0])

        # Create a DataFrame for easier processing
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])

        # Get min and max timestamps
        min_timestamp = df['timestamp'].min()
        max_timestamp = df['timestamp'].max()

        # Create a complete hourly range
        complete_range = pd.date_range(start=min_timestamp, end=max_timestamp, freq='h')

        # Create a new DataFrame with the complete range
        complete_df = pd.DataFrame({'timestamp': complete_range})

        # Merge with the original data
        merged_df = pd.merge(complete_df, df, on='timestamp', how='left')

        # Interpolate missing values
        merged_df['demand'] = merged_df['demand'].interpolate(method='linear')

        return [(row['timestamp'], row['demand']) for _, row in merged_df.iterrows()]

    def handle_cumulative_data(self, data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
        """
        Convert cumulative data to hourly rates

        Args:
            data: List of (timestamp, cumulative_demand) tuples

        Returns:
            List of (timestamp, hourly_demand) tuples
        """
        if not data:
            return []

        # Sort by timestamp
        data.sort(key=lambda x: x[0])

        # Check if data is cumulative (always increasing)
        demand_values = [d for _, d in data]
        is_cumulative = all(demand_values[i] <= demand_values[i+1] for i in range(len(demand_values)-1))

        if is_cumulative:
            logger.info("Detected cumulative data, converting to hourly rates")

            # Calculate differences
            hourly_data = []
            for i in range(1, len(data)):
                prev_ts, prev_demand = data[i-1]
                curr_ts, curr_demand = data[i]

                # Calculate time difference in hours
                time_diff = (curr_ts - prev_ts).total_seconds() / 3600.0

                if time_diff > 0:
                    # Calculate hourly rate
                    hourly_rate = (curr_demand - prev_demand) / time_diff
                    hourly_data.append((curr_ts, hourly_rate))

            return hourly_data
        else:
            # Data is not cumulative, return as is
            return data

    def is_leap_year(self, year: int) -> bool:
        """
        Check if a year is a leap year

        Args:
            year: The year to check

        Returns:
            True if the year is a leap year, False otherwise
        """
        return calendar.isleap(year)

    def generate_full_year_data(self, data: List[Tuple[datetime, float]], year: int) -> List[Tuple[datetime, float]]:
        """
        Generate a full year of hourly data based on existing data patterns,
        preserving all original data points and ensuring continuity between actual and generated data.

        Args:
            data: List of (timestamp, demand) tuples
            year: The year to generate data for

        Returns:
            List of (timestamp, demand) tuples for the entire year
        """
        logger.info(f"Generating full year data for {year}")

        # Performance optimization: Check data size and use appropriate strategy
        total_hours_in_year = 8760
        data_size = len(data) if data else 0

        if data_size > 4000:  # Large dataset - use optimized approach
            logger.warning(f"Large dataset detected ({data_size} points). Using optimized expansion.")
            return self._optimized_data_expansion(data, year)
        elif data_size > 2000:  # Medium dataset - use progressive approach
            logger.info(f"Medium dataset detected ({data_size} points). Using progressive expansion.")
            return self._progressive_data_expansion(data, year)

        # Check if it's a leap year
        is_leap = self.is_leap_year(year)
        logger.info(f"Year {year} is leap year: {is_leap}")

        # Create a complete date range for the entire year
        start_date = datetime(year, 1, 1).date()
        end_date = datetime(year, 12, 31).date()

        # Calculate the average scale of existing data to use for normalization
        avg_scale = 0
        if data:
            avg_scale = sum(demand for _, demand in data) / len(data)
            logger.info(f"Average scale of existing data: {avg_scale:.4f}")

        # If we have no data, create a default pattern with appropriate scale
        if not data:
            logger.warning("No existing data to base patterns on. Creating default pattern.")
            result = []
            # Use a reasonable default scale if we have no data
            default_scale = 50.0

            # Create a simple pattern with higher demand during day hours
            for day in pd.date_range(start=start_date, end=end_date).date:
                for hour in range(24):
                    # Create a simple pattern: higher during day (8am-8pm), lower at night
                    if 8 <= hour <= 20:
                        demand = 0.7 + (hour - 8) * 0.02  # Peaks at 2pm-3pm (relative values)
                        if hour > 14:
                            demand = 0.7 + (20 - hour) * 0.02  # Decreases after peak
                    else:
                        demand = 0.4  # Base load at night (relative value)

                    # Add some variation based on day of week (weekends lower)
                    weekday = day.weekday()
                    if weekday >= 5:  # Weekend
                        demand *= 0.8

                    # Add seasonal variation
                    month = day.month
                    if month in [12, 1, 2]:  # Winter
                        demand *= 1.2
                    elif month in [6, 7, 8]:  # Summer
                        demand *= 1.1

                    # Scale to the default scale
                    demand *= default_scale

                    timestamp = datetime.combine(day, time(hour=hour))
                    result.append((timestamp, demand))
            return result

        # Create a dictionary of existing data for quick lookup
        # Key is (year, month, day, hour)
        existing_data = {}
        existing_timestamps = set()
        for ts, demand in data:
            key = (ts.year, ts.month, ts.day, ts.hour)
            existing_data[key] = demand
            existing_timestamps.add(ts)

        logger.info(f"Preserving {len(existing_data)} original data points")

        # If we have some data, use it to create patterns for missing timestamps
        # Convert to DataFrame for easier processing
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])

        # Extract date and hour components
        df['date'] = df['timestamp'].dt.date
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month

        # Group by date first to get daily averages
        daily_avg = df.groupby('date')['demand'].mean().to_dict()

        # Add relative demand column
        df['relative_demand'] = df.apply(
            lambda row: row['demand'] / daily_avg.get(row['date'], avg_scale or 1.0),
            axis=1
        )

        # Calculate patterns based on relative values
        hourly_pattern = df.groupby(['hour', 'day_of_week'])['relative_demand'].mean().reset_index()

        # Calculate monthly scaling factors based on absolute values
        monthly_factors = df.groupby('month')['demand'].mean().reset_index()

        # Normalize to average = 1.0
        avg_demand = monthly_factors['demand'].mean() if not monthly_factors.empty else 1.0
        monthly_factors['factor'] = monthly_factors['demand'] / (avg_demand or 1.0)

        # Create dictionaries for easy lookup
        hourly_patterns = {(row['hour'], row['day_of_week']): row['relative_demand']
                          for _, row in hourly_pattern.iterrows()}
        monthly_scaling = {row['month']: row['factor'] for _, row in monthly_factors.iterrows()}

        # Fill in any missing patterns with defaults
        for hour in range(24):
            for day in range(7):
                if (hour, day) not in hourly_patterns:
                    # Use a default pattern if we don't have data for this hour/day
                    if 8 <= hour <= 20:
                        hourly_patterns[(hour, day)] = 1.2  # Higher during day (relative to daily average)
                    else:
                        hourly_patterns[(hour, day)] = 0.8  # Lower at night (relative to daily average)

        # Fill in any missing monthly factors
        for month in range(1, 13):
            if month not in monthly_scaling:
                if month in [12, 1, 2]:  # Winter
                    monthly_scaling[month] = 1.2
                elif month in [6, 7, 8]:  # Summer
                    monthly_scaling[month] = 1.1
                else:
                    monthly_scaling[month] = 1.0

        # Find the last actual data point for continuity
        if data:
            last_actual_timestamp, last_actual_demand = max(data, key=lambda x: x[0])
            last_actual_date = last_actual_timestamp.date()
            last_actual_hour = last_actual_timestamp.hour
            last_actual_day_of_week = last_actual_timestamp.weekday()
            last_actual_month = last_actual_timestamp.month

            # Calculate what the model would have predicted for this point
            base_pattern = hourly_patterns.get((last_actual_hour, last_actual_day_of_week), 1.0)
            base_scaling = monthly_scaling.get(last_actual_month, 1.0)
            model_prediction = base_pattern * base_scaling * avg_scale

            # Calculate continuity factor (how much to adjust generated data)
            if model_prediction > 0:
                continuity_factor = last_actual_demand / model_prediction
            else:
                continuity_factor = 1.0

            logger.info(f"Continuity factor for generated data: {continuity_factor:.4f}")
        else:
            continuity_factor = 1.0

        # Generate full year data, preserving original values
        result = []
        for day in pd.date_range(start=start_date, end=end_date).date:
            for hour in range(24):
                timestamp = datetime.combine(day, time(hour=hour))
                key = (timestamp.year, timestamp.month, timestamp.day, timestamp.hour)

                # If we have original data for this timestamp, use it
                if key in existing_data:
                    demand = existing_data[key]
                    result.append((timestamp, demand))
                else:
                    # Otherwise, generate a value based on patterns
                    day_of_week = day.weekday()
                    month = day.month

                    # Get relative pattern for this hour and day of week
                    relative_pattern = hourly_patterns.get((hour, day_of_week), 1.0)

                    # Apply monthly scaling
                    monthly_factor = monthly_scaling.get(month, 1.0)

                    # Calculate base demand using the average scale
                    base_demand = relative_pattern * monthly_factor * avg_scale

                    # Apply continuity factor to ensure smooth transition
                    adjusted_demand = base_demand * continuity_factor

                    # Add a small random variation (±5%)
                    variation = 0.95 + np.random.random() * 0.1
                    final_demand = adjusted_demand * variation

                    # Add a flag to indicate this is generated data
                    result.append((timestamp, final_demand))

        return result

    def process_data(self, data: List[Tuple[datetime, float]], unit: str, expand_to: str = 'year') -> List[Dict[str, Any]]:
        """
        Process the data and return a list of dictionaries with Date, Time, and Demand fields

        Args:
            data: List of (timestamp, demand) tuples
            unit: Unit of the demand values ('MW', 'kW', etc.)
            expand_to: How to expand the data: 'none', 'month', or 'year' (default: 'year')

        Returns:
            List of dictionaries with Date, Time, and Demand fields
        """
        import time
        start_time = time.time()
        processing_timeout = 300  # 5 minutes timeout

        logger.info(f"Starting data processing with {len(data)} input points")
        # Check for cumulative data
        processed_data = self.handle_cumulative_data(data)

        # Timeout check
        if time.time() - start_time > processing_timeout:
            raise TimeoutError(f"Data processing exceeded {processing_timeout} seconds timeout")

        # Detect and handle outliers
        processed_data = self.detect_and_handle_outliers(processed_data)

        # Timeout check
        if time.time() - start_time > processing_timeout:
            raise TimeoutError(f"Data processing exceeded {processing_timeout} seconds timeout")

        # Aggregate to hourly if needed
        has_sub_hourly = any(ts.minute != 0 for ts, _ in processed_data)
        if has_sub_hourly:
            processed_data = self.aggregate_to_hourly(processed_data)

        # Determine the year to use
        if processed_data:
            timestamps = [ts for ts, _ in processed_data]
            min_ts = min(timestamps)
            year = min_ts.year
        else:
            # If no data, use current year
            year = datetime.now().year

        # Expand data based on the expand_to parameter
        if expand_to.lower() == 'none':
            logger.info("Skipping data expansion as requested")
            # Just fill in missing hours within the existing date range
            processed_data = self.fill_missing_hours(processed_data)
        elif expand_to.lower() == 'month':
            logger.info("Expanding data to full months")
            # Determine the month range
            if processed_data:
                timestamps = [ts for ts, _ in processed_data]
                min_ts = min(timestamps)
                max_ts = max(timestamps)
                # Expand to full months
                start_date = datetime(min_ts.year, min_ts.month, 1)
                if max_ts.month == 12:
                    end_date = datetime(max_ts.year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = datetime(max_ts.year, max_ts.month + 1, 1) - timedelta(days=1)
                # Fill missing days within this range
                processed_data = self.fill_missing_days(processed_data, start_date, end_date)
                # Fill missing hours
                processed_data = self.fill_missing_hours(processed_data)
            else:
                # If no data, generate a full month for the current month
                now = datetime.now()
                start_date = datetime(now.year, now.month, 1)
                if now.month == 12:
                    end_date = datetime(now.year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = datetime(now.year, now.month + 1, 1) - timedelta(days=1)
                # Generate data for this month
                processed_data = self.generate_full_year_data(processed_data, year)
                # Filter to just this month
                processed_data = [(ts, demand) for ts, demand in processed_data
                                 if start_date <= ts <= end_date]
        else:  # 'year' or any other value
            # Check if we already have a full year of data
            if len(processed_data) >= 8760:  # Full year or more
                logger.info(f"Data already contains {len(processed_data)} points (full year or more), skipping expansion")
                # Just ensure we have exactly 8760 points for a clean year
                if len(processed_data) > 8760:
                    logger.info(f"Trimming data to exactly 8760 points")
                    processed_data = processed_data[:8760]
            else:
                # Timeout check before expensive operation
                if time.time() - start_time > processing_timeout:
                    raise TimeoutError(f"Data processing exceeded {processing_timeout} seconds timeout")

                logger.info(f"Generating full year data for {year} (current: {len(processed_data)} points)")
                # Generate full year data
                processed_data = self.generate_full_year_data(processed_data, year)

        # Sort the data to ensure it's in chronological order
        processed_data.sort(key=lambda x: x[0])

        # Convert values to MW based on the unit (no scaling)
        mw_data = []
        for ts, demand in processed_data:
            # Convert to MW
            mw_value = self._convert_to_mw(demand, unit)
            mw_data.append((ts, mw_value))

        logger.info(f"Converted values from {unit} to MW (no scaling applied)")

        # Convert to list of dictionaries with separate Date and Time fields
        result = []
        for ts, demand in mw_data:
            # Format date as "MM/DD/YY"
            date_str = ts.strftime('%m/%d/%y')
            # Format time as "HH:MM"
            time_str = ts.strftime('%H:%M')
            # Add entry to result list
            result.append({
                "Date": date_str,
                "Time": time_str,
                "Demand": round(demand, 2),  # Round to 2 decimal places
                "IsGenerated": ts not in set(t for t, _ in data)  # Flag to indicate generated data
            })

        return result

    def _optimized_data_expansion(self, data: List[Tuple[datetime, float]], year: int) -> List[Tuple[datetime, float]]:
        """
        Optimized data expansion for large datasets - minimal processing overhead

        Args:
            data: List of (timestamp, demand) tuples
            year: Year to generate data for

        Returns:
            List of (timestamp, demand) tuples with minimal expansion
        """
        logger.info("Using optimized expansion for large dataset")

        if not data:
            return self._generate_minimal_year_data(year)

        # Create lookup for existing data
        existing_data = {(ts.year, ts.month, ts.day, ts.hour): demand for ts, demand in data}

        # Calculate simple statistics
        demands = [demand for _, demand in data]
        avg_demand = sum(demands) / len(demands)

        # Simple hourly pattern (just day vs night)
        day_factor = 1.2  # 20% higher during day
        night_factor = 0.8  # 20% lower at night

        result = []
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 0, 0)

        # Generate only missing hours with simple pattern
        current = start_date
        while current <= end_date:
            key = (current.year, current.month, current.day, current.hour)

            if key in existing_data:
                # Use existing data
                result.append((current, existing_data[key]))
            else:
                # Generate simple pattern
                if 6 <= current.hour <= 18:  # Day hours
                    demand = avg_demand * day_factor
                else:  # Night hours
                    demand = avg_demand * night_factor

                # Add small random variation
                variation = 0.95 + (hash(str(current)) % 100) / 1000  # Deterministic "random"
                demand *= variation

                result.append((current, demand))

            current += timedelta(hours=1)

        logger.info(f"Generated {len(result)} data points using optimized method")
        return result

    def _progressive_data_expansion(self, data: List[Tuple[datetime, float]], year: int) -> List[Tuple[datetime, float]]:
        """
        Progressive data expansion for medium datasets - balanced approach

        Args:
            data: List of (timestamp, demand) tuples
            year: Year to generate data for

        Returns:
            List of (timestamp, demand) tuples with progressive expansion
        """
        logger.info("Using progressive expansion for medium dataset")

        if not data:
            return self._generate_minimal_year_data(year)

        # Create lookup for existing data
        existing_data = {(ts.year, ts.month, ts.day, ts.hour): demand for ts, demand in data}

        # Calculate basic patterns
        df = pd.DataFrame(data, columns=['timestamp', 'demand'])
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek

        # Simple hourly patterns
        hourly_avg = df.groupby('hour')['demand'].mean().to_dict()
        daily_avg = df.groupby('day_of_week')['demand'].mean().to_dict()
        overall_avg = df['demand'].mean()

        # Fill missing patterns with defaults
        for hour in range(24):
            if hour not in hourly_avg:
                if 6 <= hour <= 18:
                    hourly_avg[hour] = overall_avg * 1.1
                else:
                    hourly_avg[hour] = overall_avg * 0.9

        for day in range(7):
            if day not in daily_avg:
                if day < 5:  # Weekday
                    daily_avg[day] = overall_avg
                else:  # Weekend
                    daily_avg[day] = overall_avg * 0.8

        result = []
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 0, 0)

        current = start_date
        while current <= end_date:
            key = (current.year, current.month, current.day, current.hour)

            if key in existing_data:
                result.append((current, existing_data[key]))
            else:
                # Generate using patterns
                hour_factor = hourly_avg[current.hour] / overall_avg
                day_factor = daily_avg[current.weekday()] / overall_avg

                demand = overall_avg * hour_factor * day_factor

                # Add variation
                variation = 0.9 + (hash(str(current)) % 200) / 1000
                demand *= variation

                result.append((current, demand))

            current += timedelta(hours=1)

        logger.info(f"Generated {len(result)} data points using progressive method")
        return result

    def _generate_minimal_year_data(self, year: int) -> List[Tuple[datetime, float]]:
        """
        Generate minimal year data when no input data is available

        Args:
            year: Year to generate data for

        Returns:
            List of (timestamp, demand) tuples with basic pattern
        """
        logger.info("Generating minimal year data pattern")

        result = []
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 0, 0)

        base_demand = 50.0  # Default base demand

        current = start_date
        while current <= end_date:
            # Simple pattern: higher during day, lower at night
            if 6 <= current.hour <= 18:
                demand = base_demand * (1.0 + 0.3 * np.sin((current.hour - 6) * np.pi / 12))
            else:
                demand = base_demand * 0.6

            # Weekend reduction
            if current.weekday() >= 5:
                demand *= 0.8

            # Seasonal variation
            month_factor = 1.0 + 0.2 * np.sin((current.month - 1) * np.pi / 6)
            demand *= month_factor

            result.append((current, demand))
            current += timedelta(hours=1)

        logger.info(f"Generated {len(result)} minimal data points")
        return result
